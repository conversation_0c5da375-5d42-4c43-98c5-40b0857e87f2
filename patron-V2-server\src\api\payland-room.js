import Room from '../models/playlandparty.js';  // Ensure the path to your model is correct



export const getRooms = async (req, res) => {
    try {
        let filter = {};
        if (req.query.parentId) {
            filter = { parentId: req.query.parentId.split(',') }
        } else if (req.query.userId) {
            filter = { userId: req.query.userId.split(',') }
        }
        const rooms = await Room.Room.find(filter).populate('categoryId', 'partyname');
        res.json(rooms);
         
        // const rooms = await Room.Room.find();
        // res.json(rooms);
    } catch (err) {
        res.status(400).send("Unable to fetch rooms from database");
        console.log(err);
    }
};

export const getRoomById = async (req, res) => {
    try {
    // let data = await category.findOne(req.params).populate('parentId','_id')
        const room = await Room.Room.findById(req.params.id).populate('categoryId' , '_id' );
        if (!room) {
            return res.status(404).send("Room not found");
        }
        res.json(room);
    } catch (err) {
        res.status(400).send("Unable to fetch party from database");
        console.log(err);
    }
};

export const postRoom = async (req, res) => {
    console.log("Request body:", req.body);

    const { roomName, location, shortDescription, totalCapacity, kidsSlot, morningTime, eveningTime, userId, active, categoryId } = req.body;
    const room_pic = req.file ? req.file.location : req.body.room_pic || null;

    let parsedDescription;
    let parsedMorningTime;
    let parsedEveningTime;
    let totalCapacityNum;

    try {
        parsedDescription = JSON.parse(shortDescription);
        parsedMorningTime = JSON.parse(morningTime);
        parsedEveningTime = JSON.parse(eveningTime);
        totalCapacityNum = Number(totalCapacity);

        if (isNaN(totalCapacityNum)) {
            throw new Error("Invalid totalCapacity format");
        }
    } catch (err) {
        return res.status(400).send("Invalid input format");
    }

    let data = new Room.Room({
        roomName,
        location,
        shortDescription: parsedDescription,
        totalCapacity: totalCapacityNum,
        kidsSlot,
        morningTime: parsedMorningTime,
        eveningTime: parsedEveningTime,
        userId,
        room_pic,
        active,
        categoryId
    });

    try {
        let result = await data.save();
        console.log("Room data saved to database");
        res.json({
            id: result.id,
            roomName: result.roomName,
            location: result.location,
            shortDescription: result.shortDescription,
            totalCapacity: result.totalCapacity,
            kidsSlot: result.kidsSlot,
            userId: result.userId,
            morningTime: result.morningTime,
            eveningTime: result.eveningTime,
            room_pic: result.room_pic,
            active: result.active,
            categoryId: result.categoryId,
        });
    } catch (err) {
        res.status(400).send("Unable to save to database");
        console.log(err);
    }
};


export const updateRoom = async (req, res) => {
    const { roomName, location, shortDescription, totalCapacity, kidsSlot, morningTime, eveningTime, userId, active, categoryId } = req.body;
    const room_pic = req.file ? req.file.location : req.body.room_pic || null;

    let parsedDescription;
    let parsedMorningTime;
    let parsedEveningTime;
    let totalCapacityNum;

    try {
        parsedDescription = JSON.parse(shortDescription);
        parsedMorningTime = JSON.parse(morningTime);
        parsedEveningTime = JSON.parse(eveningTime);
        totalCapacityNum = Number(totalCapacity);

        if (isNaN(totalCapacityNum)) {
            throw new Error("Invalid totalCapacity format");
        }
    } catch (err) {
        return res.status(400).send("Invalid input format");
    }

    try {
        const updatedRoom = await Room.Room.findByIdAndUpdate(
            req.params.id,
            {
                roomName,
                location,
                shortDescription: parsedDescription,
                totalCapacity: totalCapacityNum,
                kidsSlot,
                morningTime: parsedMorningTime,
                eveningTime: parsedEveningTime,
                userId,
                room_pic,
                active,
                categoryId
            },
            { new: true }
        );

        if (!updatedRoom) {
            return res.status(404).send("Room not found");
        }

        res.json(updatedRoom);
    } catch (err) {
        res.status(400).send("Unable to update room in database");
        console.log(err);
    }
};
export const deleteRoom = async (req, res) => {
    try {
        const deletedRoom = await Room.Room.findByIdAndDelete(req.params.id);

        if (!deletedRoom) {
            return res.status(404).send("Room not found");
        }

        res.json({ message: "Room successfully deleted" });
    } catch (err) {
        res.status(400).send("Unable to delete room from database");
        console.log(err);
    }
};

