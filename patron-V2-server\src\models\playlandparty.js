import mongoose, { mongo } from 'mongoose';
const partySchema = new mongoose.Schema({

    partyname: {
        type: String
    },

    party_pic: {
        type: String
    },
    shortDescription: {
        type: String
    },
    longDescription: {
        type: []
    },
    totalCapacity: {
        type: Number
    },
    price: {
        type: Number
    },
    ExtraCapPrice:{
        type: Number
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'user'
    },
    creatdateFormat: {
        type: Date,
        default: Date.now
    },
    active:{
        type: String
    }
})


const RoomSchema = new mongoose.Schema({
    roomName: {
        type: String
    },
    location: {
        type: String
    },
    shortDescription:{
        type: []
    },
    active:{
        type: String
    },
    totalCapacity: {
        type: Number
    },
    kidsSlot: {
        type: String
    },
    morningTime: {
        type: String
    },
    eveningTime: {
        type:String
    },
    room_pic:{
        type:String
    },
    userId:{
        type:mongoose.Schema.Types.ObjectId,
        required:true,
        ref:'user'
    },
   orderCapicity:{
    type:Number,
    default:0
   },
    categoryId: {
        type: mongoose.Schema.Types.ObjectId,
        ref:'Party'
    },
    
},
    { timestamps: true })

const party = mongoose.model("Party", partySchema);
const Room=mongoose.model('room',RoomSchema)
export default {party,Room};