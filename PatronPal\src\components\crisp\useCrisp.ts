// src/components/crisp/useCrisp.ts
import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Swal from 'sweetalert2';
import { selectIsAuthenticated } from '../../redux-store/slices/customerSlice';

// Declare global types for Crisp
declare global {
  interface Window {
    $crisp: any[];
    CRISP_WEBSITE_ID: string;
    CRISP_READY?: boolean;
  }
}

export const useCrisp = (): void => {
  const location = useLocation();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const initialized = useRef(false);
  const previousAuthState = useRef<boolean | null>(null);

  useEffect(() => {
    // Only initialize once
    if (initialized.current) return;

    console.log('Initializing Crisp chat...');

    // Initialize Crisp variables on window object
    window.$crisp = [];
    window.CRISP_WEBSITE_ID = "8a3d345b-f394-428f-85b1-52ecbf3ef8ee";

    // Create and append script element
    const script = document.createElement("script");
    script.src = "https://client.crisp.chat/l.js";
    script.async = true;
    document.getElementsByTagName("head")[0].appendChild(script);

    initialized.current = true;

    // Set up authentication check - MINIMAL SETUP TO AVOID AI INTERFERENCE
    const setupCrispFeatures = () => {
      if (window.$crisp) {
        // Authentication check only
        window.$crisp.push(["on", "chat:opened", function() {
          console.log('Chat opened, checking authentication...');
          const currentAuth = JSON.parse(localStorage.getItem('customer') || 'null');
          if (!currentAuth) {
            console.log('User not authenticated, closing chat');
            window.$crisp.push(["do", "chat:close"]);

            // Show SweetAlert2 warning modal
            Swal.fire({
              title: 'Authentication Required',
              html: `
                <div style="text-align: center;">
                  <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEyIDJMMTMuMDkgOC4yNkwyMCA5TDEzLjA5IDE1Ljc0TDEyIDIyTDEwLjkxIDE1Ljc0TDQgOUwxMC45MSA4LjI2TDEyIDJaIiBzdHJva2U9IiNmOTczMTYiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiBzdHJva2U9IiNmOTczMTYiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8L3N2Zz4K" alt="Warning" style="width: 64px; height: 64px; margin-bottom: 16px;">
                  <p style="font-size: 16px; color: #333; margin: 0;">Please sign in to chat with us</p>
                  <p style="font-size: 14px; color: #666; margin-top: 8px;">You'll be redirected to the login page</p>
                </div>
              `,
              icon: 'warning',
              showConfirmButton: true,
              confirmButtonText: 'Go to Login',
              confirmButtonColor: '#f97316',
              showCancelButton: true,
              cancelButtonText: 'Cancel',
              cancelButtonColor: '#6b7280',
              width: '500px',
              heightAuto: false,
              padding: '3rem',
              customClass: {
                popup: 'swal2-center-custom',
                title: 'swal2-title-custom'
              },
              backdrop: true,
              allowOutsideClick: false,
              didOpen: (popup) => {
                popup.style.minHeight = '350px';
                popup.style.display = 'flex';
                popup.style.flexDirection = 'column';
                popup.style.justifyContent = 'center';
              }
            }).then((result) => {
              if (result.isConfirmed) {
                // User clicked "Go to Login"
                window.location.href = '/login';
              }
              // If user clicked cancel or closed modal, do nothing
            });
          } else {
            // Set user data for better AI context
            window.$crisp.push(["set", "user:email", currentAuth.email]);
            window.$crisp.push(["set", "user:nickname", currentAuth.firstname + ' ' + currentAuth.lastname]);
            console.log('User authenticated, chat ready');
          }
        }]);

        // Handle AI escalation to human support
        window.$crisp.push(["on", "message:received", function(message: any) {
          console.log('Message received:', message);

          // Check if AI is asking for clarification (indicating it needs help)
          if (message.content && typeof message.content === 'string') {
            const needsHelpPhrases = [
              "Let me connect you with support for further assistance"
            ];

            const needsHelp = needsHelpPhrases.some(phrase =>
              message.content.toLowerCase().includes(phrase.toLowerCase())
            );

            if (needsHelp) {
              console.log('AI needs help - escalating to support team');

              // Wait a moment then escalate
              setTimeout(() => {
                // Invite support team
                window.$crisp.push(["do", "trigger:run", ["support_escalation"]]);

                // Send escalation message
                window.$crisp.push(["do", "message:send", [
                  "text",
                  "Let me connect you with our support team who can better assist you. They'll be with you shortly! 👥"
                ]]);

                // Notify support team (this will send notifications to your invited agents)
                window.$crisp.push(["do", "session:event", ["escalated_to_human", {
                  reason: "AI_UNCERTAINTY",
                  timestamp: new Date().toISOString()
                }]]);

                // Set flag to track escalation
                (window as any).supportEscalated = true;
                (window as any).escalationTime = Date.now();

                // Start 30-second timeout for support response
                setTimeout(() => {
                  // Check if support team has responded
                  if ((window as any).supportEscalated && !window.$crisp.push(["get", "session:ongoing"])) {
                    console.log('Support team timeout - showing fallback message');

                    // Send fallback message
                    window.$crisp.push(["do", "message:send", [
                      "text",
                      "Sorry, our support team is currently busy. If you have product-related questions, feel free to ask me! I can help with menu items, pricing, delivery information, and more. 🍽️"
                    ]]);

                    // Reset escalation flag
                    (window as any).supportEscalated = false;
                  }
                }, 30000); // 30 seconds

              }, 2000);
            }
          }
        }]);

        console.log('Crisp setup complete - AI with escalation ready');

        // Add function to clear chat (for testing)
        (window as any).clearCrispChat = () => {
          window.$crisp.push(["do", "session:reset"]);
          console.log('Chat cleared');
        };

        // Auto-clear chat when user logs out (optional)
        (window as any).clearChatOnLogout = () => {
          window.$crisp.push(["do", "session:reset"]);
          window.$crisp.push(["do", "chat:close"]);
          console.log('Chat cleared on logout');
        };

        // Manual escalation to support team
        (window as any).escalateToSupport = () => {
          window.$crisp.push(["do", "message:send", [
            "text",
            "🔄 Connecting you with our support team for personalized assistance..."
          ]]);

          // Trigger support notification
          window.$crisp.push(["do", "session:event", ["manual_escalation", {
            reason: "USER_REQUESTED",
            timestamp: new Date().toISOString()
          }]]);

          console.log('Manually escalated to support team');
        };
      }
    };

    // Single setup attempt to avoid conflicts
    setTimeout(setupCrispFeatures, 2000);

  }, []);

  // Handle chat visibility based on current page
  useEffect(() => {
    const hiddenPaths = ['/login', '/signup', '/verify-otp', '/forget-password', '/reset-password'];
    const shouldHideChat = hiddenPaths.includes(location.pathname);

    const updateChatVisibility = () => {
      if (window.$crisp) {
        if (shouldHideChat) {
          console.log('Hiding chat for path:', location.pathname);
          window.$crisp.push(["do", "chat:hide"]);
        } else {
          console.log('Showing chat for path:', location.pathname);
          window.$crisp.push(["do", "chat:show"]);
        }
      }
    };

    // Update visibility immediately and with delays
    updateChatVisibility();
    const timer1 = setTimeout(updateChatVisibility, 500);
    const timer2 = setTimeout(updateChatVisibility, 2000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, [location.pathname]);

  // Handle logout - clear chat when user logs out
  useEffect(() => {
    // Skip on initial load
    if (previousAuthState.current === null) {
      previousAuthState.current = isAuthenticated;
      return;
    }

    // Detect logout (was authenticated, now not authenticated)
    if (previousAuthState.current === true && isAuthenticated === false) {
      console.log('User logged out - clearing Crisp chat');

      // Clear the chat session
      if (window.$crisp) {
        window.$crisp.push(["do", "session:reset"]);
        window.$crisp.push(["do", "chat:close"]);
        console.log('Crisp chat cleared on logout');
      }
    }

    // Update previous state
    previousAuthState.current = isAuthenticated;
  }, [isAuthenticated]);
};