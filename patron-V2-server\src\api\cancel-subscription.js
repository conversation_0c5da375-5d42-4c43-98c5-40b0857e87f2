import cancelsub from '../models/cancel-subscription.js';

export const getCancelSubs = async (req, res) => {
    let filter={}
    if(req.query.userId){
     filter={userId:req.query.userId.split(',')}
    }
    let data = await cancelsub.find(filter);
    res.send(data);
}
export const getCancelSub = async (req, res) => {
    let data = await cancelsub.findOne(req.params);
    res.send(data);
}

export const postCancelSub = async (req, res) => {
    const { name,email,reason,userId} = req.body;
    const data = await new cancelsub({ name,email,reason,userId});
    await data.save().then(result => {
        console.log(result, "CancelSub data save to database")
        res.json({
            name:result.name,
            email:result.email,
            reason:result.reason,
            userId:result.userId
        })
    }).catch(err => {
        res.status(400).send('unable to save database');
        console.log(err)
    })
}
export const updateCancelSub = async (req, res) => {
    console.log(req.params.id)
    let data = await cancelsub.findByIdAndUpdate(
        { _id: req.body._id },
        {
            $set: req.body
        });
    if (data) {
        res.send({ message: "CancelSub data updated successfully" });
    }
    else {
        res.send({ message: "CancelSub data cannot be updated successfully" })
    }
}
export const deleteCancelSub = async (req, res) => {
    console.log(req.params)
    let data = await cancelsub.deleteOne(req.params)
    if (data) {
        res.send({ message: "CancelSub data delete successfully" });
    }
    else {
        res.send({ message: "CancelSub data cannot delete successfully" })
    }
}