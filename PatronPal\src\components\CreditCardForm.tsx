import React, { useState } from 'react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import Swal from 'sweetalert2';

interface CreditCardFormProps {
  onPaymentSuccess: (paymentIntentId: string, chargeId: string) => void;
  onPaymentError: (error: string) => void;
  amount: number;
  currency: string;
  stripeAccount: string;
  applicationFeeAmount: number;
  description: string;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const CreditCardForm: React.FC<CreditCardFormProps> = ({
  onPaymentSuccess,
  onPaymentError,
  amount,
  currency,
  stripeAccount,
  applicationFeeAmount,
  description,
  isLoading,
  setIsLoading
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const [cardError, setCardError] = useState<string>('');

  const cardElementOptions = {
    style: {
      base: {
        fontSize: '16px',
        color: '#424770',
        '::placeholder': {
          color: '#aab7c4',
        },
        padding: '12px',
      },
      invalid: {
        color: '#9e2146',
      },
    },
    hidePostalCode: false,
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      return;
    }

    setIsLoading(true);
    setCardError('');

    try {
      // Step 1: Create Payment Intent
      const baseUrl = import.meta.env.VITE_LOCAL_API_URL || 'http://localhost:4444/api/v1';
      const paymentIntentResponse = await fetch(`${baseUrl}/paymentIntent`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          currency,
          stripeAccount,
          application_fee_amount: applicationFeeAmount,
          description,
        }),
      });

      const { paymentIntent } = await paymentIntentResponse.json();

      if (!paymentIntent) {
        throw new Error('Failed to create payment intent');
      }

      // Step 2: Confirm Payment Intent with card
      const { error, paymentIntent: confirmedPaymentIntent } = await stripe.confirmCardPayment(
        paymentIntent.client_secret,
        {
          payment_method: {
            card: cardElement,
          },
        }
      );

      if (error) {
        console.error('Payment confirmation error:', error);
        setCardError(error.message || 'Payment failed');
        onPaymentError(error.message || 'Payment failed');
        
        Swal.fire({
          title: 'Payment Failed',
          text: error.message || 'Payment failed. Please try again.',
          icon: 'error',
          confirmButtonText: 'OK',
          confirmButtonColor: '#FF5C00',
          timer: 3000,
          timerProgressBar: true
        });
      } else if (confirmedPaymentIntent.status === 'requires_capture') {
        // Step 3: Capture the payment
        const captureResponse = await fetch(`${baseUrl}/capture_payment`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            payment_intent_id: confirmedPaymentIntent.id,
          }),
        });

        const capturedPayment = await captureResponse.json();

        if (capturedPayment.status === 'succeeded') {
          console.log('Payment succeeded:', capturedPayment);
          
          Swal.fire({
            title: 'Payment Successful!',
            text: 'Your payment has been processed successfully.',
            icon: 'success',
            confirmButtonText: 'OK',
            confirmButtonColor: '#FF5C00',
            timer: 2000,
            timerProgressBar: true
          });

          // Keep loading state active - will be handled by parent component
          onPaymentSuccess(capturedPayment.id, capturedPayment.charges?.data?.[0]?.id || '');
        } else {
          throw new Error('Payment capture failed');
        }
      } else {
        console.log('Payment succeeded without capture:', confirmedPaymentIntent);

        // Keep loading state active - will be handled by parent component
        onPaymentSuccess(confirmedPaymentIntent.id, (confirmedPaymentIntent as any).charges?.data?.[0]?.id || '');
      }
    } catch (error: any) {
      console.error('Payment error:', error);
      const errorMessage = error.message || 'Payment failed. Please try again.';
      setCardError(errorMessage);
      onPaymentError(errorMessage);
      
      Swal.fire({
        title: 'Payment Error',
        text: errorMessage,
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#FF5C00',
        timer: 3000,
        timerProgressBar: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCardChange = (event: any) => {
    if (event.error) {
      setCardError(event.error.message);
    } else {
      setCardError('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="bg-white p-4 rounded-lg border border-gray-300">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Card Information
        </label>
        <div className="border border-gray-300 rounded-md p-3">
          <CardElement
            options={cardElementOptions}
            onChange={handleCardChange}
          />
        </div>
        {cardError && (
          <div className="text-red-500 text-sm mt-2">{cardError}</div>
        )}
      </div>
      
      <button
        type="submit"
        disabled={!stripe || isLoading}
        className={`w-full py-3 px-4 rounded-md font-medium text-white transition-colors ${
          isLoading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-[#FF5C00] hover:bg-[#E54A00] focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 cursor-pointer'
        }`}
      >
        {isLoading ? 'Processing...' : `Pay $${(amount / 100).toFixed(2)}`}
      </button>
    </form>
  );
};

export default CreditCardForm;
