// Web-based review form template
export const generateReviewFormTemplate = (deviceId, customerId, orderId, businessName, apiBaseUrl, existingReview = null) => {
  const isUpdate = existingReview !== null;
  const currentRating = existingReview ? existingReview.rating : 0;
  const currentTestimonial = existingReview ? existingReview.testimonial : '';
  const formTitle = isUpdate ? 'Update Your Review' : 'Leave a Review';
  const submitButtonText = 'Submit Review'; // Always show "Submit Review"
  return `
  <!DOCTYPE html>
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Leave a Review - ${businessName}</title>
      <style>
        body {
          font-family: 'Poppins', Arial, sans-serif;
          margin: 0;
          padding: 20px;
          background-color: #f8f9fa;
          color: #333;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          background: white;
          padding: 40px;
          border-radius: 10px;
          box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
          text-align: center;
          margin-bottom: 30px;
        }
        .header h1 {
          color: #068af5;
          margin-bottom: 10px;
        }
        .header p {
          color: #666;
          font-size: 16px;
        }
        .star-rating {
          text-align: center;
          margin-bottom: 30px;
        }
        .star-rating input[type="radio"] {
          display: none;
        }
        .star-rating label {
          font-size: 40px;
          color: #ddd;
          cursor: pointer;
          margin: 0 5px;
          transition: color 0.2s;
          display: inline-block;
        }
        .star-rating label:hover,
        .star-rating label.active {
          color: #ffc107;
        }
        .rating-text {
          text-align: center;
          margin-top: 10px;
          font-size: 18px;
          font-weight: bold;
          color: #068af5;
          min-height: 25px;
        }
        .form-group {
          margin-bottom: 20px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 8px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 5px;
          font-size: 14px;
          resize: vertical;
          min-height: 100px;
          font-family: inherit;
          box-sizing: border-box;
        }
        .form-textarea:focus {
          border-color: #068af5;
          outline: none;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 15px 40px;
          border: none;
          border-radius: 5px;
          font-size: 16px;
          font-weight: bold;
          cursor: pointer;
          transition: background-color 0.2s;
          width: 100%;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .submit-button:disabled {
          background-color: #ccc;
          cursor: not-allowed;
        }
        .business-info {
          text-align: center;
          margin-bottom: 30px;
          padding: 20px;
          background-color: #f0f8ff;
          border-radius: 8px;
          border: 2px solid #068af5;
        }
        .business-info h2 {
          color: #068af5;
          margin: 0 0 10px 0;
        }
        .error-message {
          color: #dc3545;
          text-align: center;
          margin-top: 10px;
          display: none;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📝 ${formTitle}</h1>
          <p>${isUpdate ? 'Update your previous review below.' : 'Your thoughts help us serve you better. Please take a moment to share your experience.'}</p>
          ${isUpdate ? '<p style="color: #068af5; font-weight: bold;">✏️ You are updating your existing review</p>' : ''}
        </div>

        <div class="business-info">
          <h2>${businessName}</h2>
          <p>Thank you for choosing us!</p>
        </div>

        <form id="reviewForm" action="${apiBaseUrl}/api/v1/device/email-review" method="POST">
          <input type="hidden" name="deviceId" value="${deviceId}">
          <input type="hidden" name="customerId" value="${customerId}">
          <input type="hidden" name="orderId" value="${orderId}">

          <div class="form-group">
            <label class="form-label">⭐ Rate Your Overall Experience</label>
            <div class="star-rating" id="starRating">
              <input type="radio" name="rating" value="1" id="star1" required ${currentRating === 1 ? 'checked' : ''}>
              <label for="star1" data-rating="1">★</label>
              <input type="radio" name="rating" value="2" id="star2" ${currentRating === 2 ? 'checked' : ''}>
              <label for="star2" data-rating="2">★</label>
              <input type="radio" name="rating" value="3" id="star3" ${currentRating === 3 ? 'checked' : ''}>
              <label for="star3" data-rating="3">★</label>
              <input type="radio" name="rating" value="4" id="star4" ${currentRating === 4 ? 'checked' : ''}>
              <label for="star4" data-rating="4">★</label>
              <input type="radio" name="rating" value="5" id="star5" ${currentRating === 5 ? 'checked' : ''}>
              <label for="star5" data-rating="5">★</label>
            </div>
            <div class="rating-text" id="ratingText"></div>
          </div>

          <div class="form-group">
            <label class="form-label" for="testimonial">Tell us more about your experience (optional)</label>
            <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your thoughts about the food, service, atmosphere, or anything else...">${currentTestimonial}</textarea>
          </div>

          <button type="submit" class="submit-button" id="submitBtn">${submitButtonText}</button>
          <div class="error-message" id="errorMessage"></div>
        </form>
      </div>

      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const starLabels = document.querySelectorAll('.star-rating label');
          const radioInputs = document.querySelectorAll('.star-rating input[type="radio"]');
          const ratingText = document.getElementById('ratingText');
          const submitBtn = document.getElementById('submitBtn');
          const errorMessage = document.getElementById('errorMessage');

          const ratingTexts = {
            1: '1/5 - Poor',
            2: '2/5 - Fair',
            3: '3/5 - Good',
            4: '4/5 - Very Good',
            5: '5/5 - Excellent'
          };

          // Initialize form with existing data if available
          const currentRating = ${currentRating};
          if (currentRating > 0) {
            updateStarDisplay(currentRating);
            ratingText.textContent = ratingTexts[currentRating];
          }
          
          // Add click event listeners to star labels
          starLabels.forEach(function(label) {
            label.addEventListener('click', function(e) {
              e.preventDefault();
              const rating = parseInt(this.getAttribute('data-rating'));

              // Check the corresponding radio button
              const radioInput = document.getElementById('star' + rating);
              if (radioInput) {
                radioInput.checked = true;
              }

              // Update visual feedback immediately
              updateStarDisplay(rating);

              // Update rating text
              ratingText.textContent = ratingTexts[rating];
            });

            // Add hover effects
            label.addEventListener('mouseenter', function() {
              const rating = parseInt(this.getAttribute('data-rating'));
              updateStarDisplay(rating);
            });
          });

          // Add mouseleave to container to restore selected state
          document.querySelector('.star-rating').addEventListener('mouseleave', function() {
            const selectedRating = document.querySelector('input[name="rating"]:checked');
            if (selectedRating) {
              updateStarDisplay(parseInt(selectedRating.value));
            } else {
              updateStarDisplay(0);
            }
          });
          
          // Add change event listeners to radio inputs
          radioInputs.forEach(function(input) {
            input.addEventListener('change', function() {
              if (this.checked) {
                const rating = parseInt(this.value);
                updateStarDisplay(rating);
                ratingText.textContent = ratingTexts[rating];
              }
            });
          });
          
          function updateStarDisplay(rating) {
            starLabels.forEach(function(label, index) {
              const starRating = index + 1;
              if (starRating <= rating) {
                label.classList.add('active');
                label.style.color = '#ffc107';
              } else {
                label.classList.remove('active');
                label.style.color = '#ddd';
              }
            });
          }
          
          // Form validation before submit
          document.getElementById('reviewForm').addEventListener('submit', function(e) {
            const selectedRating = document.querySelector('input[name="rating"]:checked');
            if (!selectedRating) {
              e.preventDefault();
              errorMessage.textContent = 'Please select a star rating before submitting your review.';
              errorMessage.style.display = 'block';
              return false;
            }
            
            // Show loading state
            submitBtn.textContent = 'Submitting...';
            submitBtn.disabled = true;
            errorMessage.style.display = 'none';
          });
        });
      </script>
    </body>
  </html>
  `;
};
