import mongoose from 'mongoose';
var current = new Date();
const timeStamp = new Date(Date.UTC(current.getFullYear(),
    current.getMonth(), current.getDate(), current.getHours(),
    current.getMinutes(), current.getSeconds(), current.getMilliseconds()));

const AllowanceSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    amount: {
        type: Number,
        required: true,
    },
    createdAt: {
        type: Date,
        // default: Date.now
    }
});

const BonusSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    amount: {
        type: Number,
        required: true,
    },
    createdAt: {
        type: Date
    }
});
        

const DeductionSchema = new mongoose.Schema({
    deduction: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Deduction',
    },
    amount: {
        type: Number,
    },
    name: {
        type: String,
        required: true,
    },
});

const employeeSchema = new mongoose.Schema({
    userName: {
        type: String,
    },
    firstName: {
        type: String,
    },
    lastName: {
        type: String,
    },
    email: {
        type: String,
    },
    phoneNo: {
        type: String,
    },
    address: {
        type: String,
    },
    employeeId: {
        type: String,
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'user',
    },
    password: {
        type: String,
    },
    confirmPassword: {
        type: String,
    },
    role: {
        type: String,
        default: 'employee',
    },
    startDate: {
        type: Date,
        default: Date.now,
    },
    hourlyRate: {
        type: Number,
    },
    overTimeRate: {
        type: Number,
    },
    totalHours: {
        type: Number,
    },
    // employeeType: {
    //     type: mongoose.Schema.Types.ObjectId,
    //     ref: 'EmployeeType',
    // },
    employeeType: {
        type: String
    },
    employeeStartTime:{
        type:String
    },
    employeeEndTime:{
       type:String
    },
    IsDeleted:{
       type:Boolean,
       default:false
    },
    allowancesHistory: [AllowanceSchema],
    bonusesHistory: [BonusSchema],
    deductions: [DeductionSchema],
}, { timestamps: true });

const EmployeeTypeSchema = new mongoose.Schema({
    name: {
        type: String,
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'user',
    },
});
const Allowance = mongoose.model("Allowance", AllowanceSchema);
const Bonus = mongoose.model("Bonus", BonusSchema);
const employee = mongoose.model("Employee", employeeSchema);
const employeeType = mongoose.model("EmployeeType", EmployeeTypeSchema);
const Deduction = mongoose.model("Deduction", DeductionSchema);

export { Allowance, Bonus,employee, employeeType, Deduction };
