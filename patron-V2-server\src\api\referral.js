import { UserReferral, CustomerReferral } from '../models/referral.js';
import PatronPalCustomer from '../models/patronpalCustomers.js';
// import User from '../models/User.js';

function generateUniqueReferralCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Customer Referral Code Generation
export const generateCustomerReferralCode = async (req, res) => {
  try {
    const { userId } = req.body;

    const user = await PatronPalCustomer.findById(userId);

    if (!user) {
      return res.status(404).json({ error: "User not found." });
    }

    const referralCode = generateUniqueReferralCode();

    const referral = new CustomerReferral({
      customerId: userId,
      referralCode: referralCode,
    });

    await referral.save();

    user.CustomerReferral.push(referral._id);
    await user.save();

    res.status(200).json({ referralCode: referralCode });
  } catch (error) {
    console.error("Error generating referral code:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Checking Customer Referral Status
export const getCustomerReferralCode = async (req, res) => {
  try {
    const { userId } = req.params;

    const referrals = await CustomerReferral.find({ customerId: userId });

    if (referrals.length === 0) {
      return res.status(404).json({ error: "Referral not found." });
    }

    const availableReferral = referrals.find(referral => !referral.points);
    
    // Count the referrals with points
    const pointsCount = referrals.filter(referral => referral.points).length;

    if (!availableReferral) {
      return res.status(404).json({ error: "No unused referral found.", pointsCount });
    }



    res.status(200).json({ referralCode: availableReferral.referralCode, pointsCount  });
  } catch (error) {
    console.error("Error checking referral status:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// User Referral Code Generation
export const generateUserReferralCode = async (req, res) => {
  try {
    const { userId } = req.body;

    const user = await PatronPalCustomer.findById(userId);
    if (!user) {
      return res.status(404).json({ error: "User not found." });
    }

    const referralCode = generateUniqueReferralCode();

    const referral = new UserReferral({
      customerId: userId,
      referralCode: referralCode,
    });

    await referral.save();

    user.usersreferrals.push(referral._id);
    await user.save();

    res.status(200).json({ referralCode: referralCode });
  } catch (error) {
    console.error("Error generating referral code:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

// Checking User Referral Status
export const getUserReferralCode = async (req, res) => {
  try {
    const { userId } = req.params;

    const referrals = await UserReferral.find({ customerId: userId });

    if (referrals.length === 0) {
      return res.status(404).json({ error: "Referral not found." });
    }

    const availableReferral = referrals.find(referral => !referral.points);
    
    // Count the referrals with points
    const pointsCount = referrals.filter(referral => referral.points).length;

    if (!availableReferral) {
      return res.status(404).json({ error: "No unused referral found.",pointsCount });
    }

    res.status(200).json({ referralCode: availableReferral.referralCode, pointsCount });
  } catch (error) {
    console.error("Error checking referral status:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};

export const getCustomerbalance = async (req, res) => {
  try {
    const { userId } = req.params;

    // Fetch customer referrals and user referrals based on userId
    const customerReferrals = await CustomerReferral.find({ customerId: userId });
    const userReferrals = await UserReferral.find({customerId: userId });

    // Check if customer referrals exist
    if (customerReferrals.length === 0 && userReferrals.length === 0) {
      return res.status(404).json({ error: "No referrals found." });
    }

    // Count the number of referrals with points
    const customerPointsCount = customerReferrals.filter(referral => referral.points).length;
    const userPointsCount = userReferrals.filter(referral => referral.points).length;

    // Calculate total balance
    const customerBalance = customerPointsCount * 5; // $5 per customer referral with points
    const userBalance = userPointsCount * 20; // $20 per user referral with points

    res.status(200).json({ customerPointsCount, userPointsCount, customerBalance, userBalance });
  } catch (error) {
    console.error("Error checking referral status:", error);
    res.status(500).json({ error: "Internal Server Error" });
  }
};
