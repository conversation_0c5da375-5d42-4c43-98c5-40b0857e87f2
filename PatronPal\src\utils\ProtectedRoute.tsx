import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectIsAuthenticated, selectCurrentCustomer, selectAuthInitialized } from '../redux-store/slices/customerSlice';

import type { ReactNode } from 'react';

interface ProtectedRouteProps {
  children: ReactNode;
  requireUserId?: boolean;
}

const ProtectedRoute = ({ children, requireUserId = false }: ProtectedRouteProps) => {
  const location = useLocation();
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const currentCustomer = useSelector(selectCurrentCustomer);
  const authInitialized = useSelector(selectAuthInitialized);

  // Show loading while auth is being initialized, but only briefly
  if (!authInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Check if user is authenticated
  if (!isAuthenticated || !currentCustomer) {
    const currentPath = location.pathname + location.search;
    return <Navigate to={`/login?redirect=${encodeURIComponent(currentPath)}`} state={{ from: location }} replace />;
  }
  
  // Additional check for routes that require userId parameter
  if (requireUserId) {
    const urlParams = window.location.pathname.split('/');
    const userIdFromUrl = urlParams.find((_param, index) => 
      urlParams[index - 1] === 'userid' || 
      (urlParams.includes('product') && index === urlParams.length - 2)
    );
    
    // Verify the userId in URL matches the current user
    if (userIdFromUrl && userIdFromUrl !== currentCustomer._id) {
      return <Navigate to="/login" state={{ from: location }} replace />;
    }
  }
  
  return children;
};

export default ProtectedRoute;