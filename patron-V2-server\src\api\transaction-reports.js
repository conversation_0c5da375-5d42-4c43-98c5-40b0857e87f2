import orderitem from '../models/orderitem.js';

export const getTransactionReportsLastMonth = async (req, res) => {
  try {
    const { userId, employeeId, startDate, endDate } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    let query = { userId: userId };
    
    if (employeeId) {
      query.employeeId = employeeId;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    } else {
      const today = new Date();
      const startOfRange = new Date(today);
      startOfRange.setDate(today.getDate() - 29);
      startOfRange.setHours(0, 0, 0, 0);

      const endOfRange = new Date(today);
      endOfRange.setHours(23, 59, 59, 999);
      
      query.createdAt = {
        $gte: startOfRange,
        $lte: endOfRange
      };
    }

    const orders = await orderitem.find(query)
      .populate({
        path: 'paymentType',
        model: 'paymentlist',
        select: 'name' 
      })
      .sort({ createdAt: -1 });

    const transactions = orders.map(order => ({
      timestamp: order.createdAt,
      transactionId: order.OrderNumber,
      items: order.product.map(item => ({
        name: item.name,
        quantity: item.quantity
      })),
      amount: order.grandTotal.toFixed(2),
      paymentType: order.paymentType?.name || "Cash",
      receipt: order.orderNo,
      status: order.Status,
      hasRefund: order.refundData && order.refundData.length > 0,
    }));

    const totalGrandTotal = orders.reduce((sum, order) => 
      sum + (order.grandTotal || 0), 0
    );
    
    const totalRefunds = orders.reduce((sum, order) => 
      sum + (order.refundTotal || 0), 0
    );
    const grossSales = totalGrandTotal + totalRefunds;
    const deductions = totalRefunds;
    const netSales = totalGrandTotal;
    const totalTransactions = orders.length;
    const completed = orders.filter(o => o.Status === 'Completed').length;
    const voided = orders.filter(o => o.Status === 'Voided').length;
    const refunded = orders.filter(o => o.refundData && o.refundData.length > 0).length;

    res.json({
      transactions,
      summary: {
        totalTransactions,
        completed,
        voided,
        refunded,
        grossSales: parseFloat(grossSales.toFixed(2)),
        deductions: parseFloat(deductions.toFixed(2)),
        netSales: parseFloat(netSales.toFixed(2))
      },
    });
    
  } catch (error) {
    console.error('Error in getTransactionReportsLastMonth:', error);
    res.status(500).json({ error: error.message });
  }
};