
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { Eye, EyeOff, Check, X } from 'lucide-react';
import { toast } from 'react-toastify';
import type { AppDispatch } from '../../redux-store/store'; 
import {
  updatePassword,
  clearError,
  selectCustomerLoading,
  selectCustomerError,
} from '../../redux-store/slices/customerSlice';


// Password validation schema
const passwordValidationSchema = Yup.object({
  password: Yup.string()
    .min(8, 'Password must be at least 8 characters')
    .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
    .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .matches(/\d/, 'Password must contain at least one number')
    .matches(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character')
    .required('Password is required'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'Passwords must match')
    .required('Please confirm your password')
});

const ResetPassword: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get email from URL parameters
  const email = searchParams.get('email') || '';
  
  // Redux selectors
  const loading = useSelector(selectCustomerLoading);
  const error = useSelector(selectCustomerError);
  
  // Local state
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const initialValues = {
    password: '',
    confirmPassword: ''
  };

  // Clear errors when component mounts
  useEffect(() => {
    dispatch(clearError());
  }, [dispatch]);

  // Handle Redux errors
  useEffect(() => {
    if (error) {
      toast.error(error);
    }
  }, [error]);

  // Redirect if no email parameter
  useEffect(() => {
    if (!email) {
      toast.error('Invalid reset password link. Please try again.');
      navigate('/forget-password');
    }
  }, [email, navigate]);

  const getPasswordStrength = (password: string) => {
    let strength = 0;
    const checks = [
      password.length >= 8,
      /[a-z]/.test(password),
      /[A-Z]/.test(password),
      /\d/.test(password),
      /[!@#$%^&*(),.?":{}|<>]/.test(password)
    ];
    
    strength = checks.filter(Boolean).length;
    
    if (strength <= 2) return { level: 'weak', color: 'bg-red-500', text: 'Weak' };
    if (strength <= 3) return { level: 'medium', color: 'bg-yellow-500', text: 'Medium' };
    if (strength <= 4) return { level: 'good', color: 'bg-blue-500', text: 'Good' };
    return { level: 'strong', color: 'bg-green-500', text: 'Strong' };
  };

  const handleSubmit = async (values: typeof initialValues) => {
    // Clear previous errors
    dispatch(clearError());
    
    try {
      const resultAction = await dispatch(updatePassword({
        email: email.trim(),
        newPassword: values.password
      }));
      
      if (resultAction.type.endsWith('/fulfilled')) {
        // Success - show success state
        setIsSuccess(true);
        toast.success('Password updated successfully!');
      } else if (resultAction.type.endsWith('/rejected')) {
        // Handle specific error cases
        const errorMessage = resultAction.payload as string;
        
        if (errorMessage?.toLowerCase().includes('invalid') || 
            errorMessage?.toLowerCase().includes('expired')) {
          toast.error('Password reset session has expired. Please request a new password reset.');
          navigate('/forget-password');
        } else {
          toast.error(errorMessage || 'Failed to update password');
        }
      }
    } catch (err) {
      console.error('Password update error:', err);
      toast.error('Something went wrong. Please try again.');
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Check className="w-8 h-8 text-green-600" />
            </div>
            <h2 className="text-2xl font-semibold text-gray-900 mb-2">
              Password Reset Successfully!
            </h2>
            <p className="text-gray-600">
              Your password has been updated. You can now sign in with your new password.
            </p>
          </div>
          
          <button
            onClick={() => navigate('/login')}
            className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-colors"
          >
            Back to Sign In
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-lg shadow-sm border border-gray-200 p-8">
        {/* Logo */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">
            Patron<span className="text-orange-500">Pal</span>
          </h1>
        </div>

        <div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Reset Your Password
          </h2>
          <p className="text-gray-600 mb-8">
            Create a new password for your account. Make sure it's strong and secure.
          </p>

          <Formik
            initialValues={initialValues}
            validationSchema={passwordValidationSchema}
            onSubmit={handleSubmit}
          >
            {({ values, errors, touched }) => (
              <Form>
                {/* New Password Field */}
                <div className="mb-6">
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    New Password
                  </label>
                  <div className="relative">
                    <Field
                      type={showPassword ? 'text' : 'password'}
                      id="password"
                      name="password"
                      placeholder="Enter new password"
                      className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors ${
                        errors.password && touched.password 
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                          : 'border-gray-300'
                      }`}
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      disabled={loading}
                    >
                      {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  
                  {/* Password Strength Indicator */}
                  {values.password && (
                    <div className="mt-2">
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrength(values.password).color}`}
                            style={{ width: `${(getPasswordStrength(values.password).level === 'weak' ? 20 : 
                                                 getPasswordStrength(values.password).level === 'medium' ? 40 : 
                                                 getPasswordStrength(values.password).level === 'good' ? 60 : 80)}%` }}
                          />
                        </div>
                        <span className={`text-xs font-medium ${
                          getPasswordStrength(values.password).level === 'weak' ? 'text-red-600' :
                          getPasswordStrength(values.password).level === 'medium' ? 'text-yellow-600' :
                          getPasswordStrength(values.password).level === 'good' ? 'text-blue-600' : 'text-green-600'
                        }`}>
                          {getPasswordStrength(values.password).text}
                        </span>
                      </div>
                      
                      {/* Password Requirements */}
                      <div className="space-y-1 text-xs">
                        {[
                          { test: values.password.length >= 8, text: 'At least 8 characters' },
                          { test: /[a-z]/.test(values.password), text: 'One lowercase letter' },
                          { test: /[A-Z]/.test(values.password), text: 'One uppercase letter' },
                          { test: /\d/.test(values.password), text: 'One number' },
                          { test: /[!@#$%^&*(),.?":{}|<>]/.test(values.password), text: 'One special character' }
                        ].map((req, index) => (
                          <div key={index} className={`flex items-center gap-2 ${req.test ? 'text-green-600' : 'text-gray-400'}`}>
                            {req.test ? <Check size={12} /> : <X size={12} />}
                            <span>{req.text}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <ErrorMessage name="password" component="div" className="mt-1 text-sm text-red-600" />
                </div>

                {/* Confirm Password Field */}
                <div className="mb-6">
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password
                  </label>
                  <div className="relative">
                    <Field
                      type={showConfirmPassword ? 'text' : 'password'}
                      id="confirmPassword"
                      name="confirmPassword"
                      placeholder="Confirm new password"
                      className={`w-full px-4 py-3 pr-12 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 outline-none transition-colors ${
                        errors.confirmPassword && touched.confirmPassword 
                          ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
                          : 'border-gray-300'
                      }`}
                      disabled={loading}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      disabled={loading}
                    >
                      {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                    </button>
                  </div>
                  
                  {/* Password Match Indicator */}
                  {values.confirmPassword && (
                    <div className={`mt-2 flex items-center gap-2 text-xs ${
                      values.password === values.confirmPassword ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {values.password === values.confirmPassword ? <Check size={12} /> : <X size={12} />}
                      <span>
                        {values.password === values.confirmPassword ? 'Passwords match' : 'Passwords do not match'}
                      </span>
                    </div>
                  )}
                  
                  <ErrorMessage name="confirmPassword" component="div" className="mt-1 text-sm text-red-600" />
                </div>

                <button
                  type="submit"
                  disabled={loading || Object.keys(errors).length > 0}
                  className="w-full bg-orange-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Updating Password...
                    </>
                  ) : (
                    'Update Password'
                  )}
                </button>
              </Form>
            )}
          </Formik>

          <div className="mt-6 text-center">
            <button
              onClick={() => navigate('/forget-password')}
              className="text-orange-500 hover:text-orange-600 font-medium transition-colors"
              disabled={loading}
            >
              ← Back to Verification
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;