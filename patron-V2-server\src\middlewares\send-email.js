import nodemailer from 'nodemailer'
import dotenv from 'dotenv'
import {google} from 'googleapis'
dotenv.config()
const REFRESH_TOKEN='1//04yyfCLUQ9z_0CgYIARAAGAQSNwF-L9Ir6696ecOAsw9-IUN1Tt0BaSzV1ayJl29xwaZqywW7OyA5sBbIqqg6lPYripXyviy33ec'
const CLIENT_ID='81639033188-h5jgoamh0m9jqht34ugeat8drjsdai4j.apps.googleusercontent.com'
const CLIENT_SECRET='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'
const REDIRECT_URI='https://developers.google.com/oauthplayground'
const OAuthClient= new google.auth.OAuth2(
    CLIENT_ID,
    CLIENT_SECRET,
    REFRESH_TOKEN
)
OAuthClient.setCredentials({refresh_token:REFRESH_TOKEN})

const sendMail = async (email, subject, html) => {
    let domain;
    if (process.env.NODE_ENV === 'production') {
        domain = true
    } else if (process.env.NODE_ENV === 'development') {
        domain = false
    }
   const token=await OAuthClient.getAccessToken();
   console.log('token: ', token);

    console.log('email 1: ', email);
    try {

        const transporter = nodemailer.createTransport({
            service: 'gmail',
            // name: ["www.patronworks.net","www.patronworks.com"],
            // name:"www.patronworks.net",
            secure: domain,
            auth: {
                type: 'OAuth2',
                user: "<EMAIL>",
                clientId:CLIENT_ID,
                clientSecret:CLIENT_SECRET,
                refreshToken:REFRESH_TOKEN,
                accessToken:token
            },
            tls:{
                rejectUnauthorized:true
            }
        })
        await transporter.sendMail({
            from:'<EMAIL>',
            to: email,
            subject: subject,
            html: html
        })
        console.log("Send Eamil Success");
    } catch (error) {
        console.log(error, "email not sent");
    }
}

export default sendMail;