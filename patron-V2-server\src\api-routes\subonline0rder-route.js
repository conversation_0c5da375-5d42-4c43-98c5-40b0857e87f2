import express from 'express';
import { GetOnlineOrderItem,
    GetOnlineOrderItemById,
    PostOnlineOrderItem,
    updateOnlineOrderItem,
    deleteOnlineOrderItem,
    getOnlineOrderItemsLastWeek,
    getOnlineKitchenOrders
} from '../api/subOnline-orderitem.js'
const app=express.Router();

app.get('/sub-orderitem',GetOnlineOrderItem);
app.get('/sub-orderitem/lastWeek',getOnlineOrderItemsLastWeek)
app.get('/sub-orderitem/:_id',GetOnlineOrderItemById);
app.post('/sub-orderitem',PostOnlineOrderItem);
app.put('/sub-orderitem/:_id',updateOnlineOrderItem);
app.delete('/sub-orderitem/:id',deleteOnlineOrderItem);
app.get('/online-kitchen-orders', getOnlineKitchenOrders)

export default app ;
