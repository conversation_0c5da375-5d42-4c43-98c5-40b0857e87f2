/* eslint-disable @typescript-eslint/no-explicit-any */
import { Plus } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import ModifierModal from "../Modal/ModifierModal";
import { useSelector } from "react-redux";
import { getModifiers, selectModifiers } from "../../redux-store/slices/modifierSlice";
import { useAppDispatch } from "../../redux-store/hooks";
import {
    addItem,
    selectCartItems,
    type CartItem
} from "../../redux-store/slices/cartSlice";
import Swal from 'sweetalert2';
import { useNavigate } from 'react-router-dom';

// Types
interface Restaurant {
    userId: any;
    id: string;
    _id?: string; // Add _id field to match ProductList.tsx
    name: string;
    cuisine: string;
    parentCategory?: string;
    rating: number;
    image: string;
    deal?: string;
    pizzaOffer?: string;
    priceOff?: string;
    price: number;
    discountPrice?: number;
    active: boolean;
    reviews?: any[];
    hasReviews?: boolean;
    categoryId?: any[];
    favorites?: Array<{
        _id?: string;
        customerId: any;
    }>;
}

interface RestaurantCardProps {
    restaurant: Restaurant;
    userId: string;
    favoriteLoading?: boolean;
    restaurantData?: any; // Add restaurant data prop
}

const RestaurantCard: React.FC<RestaurantCardProps> = ({
    restaurant,
    userId,
    restaurantData // This contains the restaurant data from TopSection
}) => {
    const dispatch = useAppDispatch();
    const navigate = useNavigate();
    const cartItems = useSelector(selectCartItems);
    const [isModifierModalOpen, setIsModifierModalOpen] = useState(false);
    const [hasModifiers, setHasModifiers] = useState<boolean | null>(null);
    const [isChecking, setIsChecking] = useState(false);
    const modifiersCheckedRef = useRef(false);

    const allModifiers = useSelector(selectModifiers);


    useEffect(() => {
        // Load modifiers when component mounts, but only once
        if (!isChecking && allModifiers.length === 0 && !modifiersCheckedRef.current && userId) {
            setIsChecking(true);
            modifiersCheckedRef.current = true;

            dispatch(getModifiers({ userId }))
                .finally(() => setIsChecking(false));
        }
    }, [dispatch, allModifiers.length, isChecking, userId]);

    useEffect(() => {
        // Check if this product has modifiers
        if (allModifiers.length > 0 && restaurant.id) {
            const productHasModifiers = allModifiers.some(modifier =>
                modifier.productId &&
                (typeof modifier.productId === 'string'
                    ? modifier.productId === restaurant.id
                    : modifier.productId._id === restaurant.id)
            );
            setHasModifiers(productHasModifiers);
        }
    }, [allModifiers, restaurant.id]);

    const handleAddClick = (e: React.MouseEvent) => {
        e.stopPropagation();

        if (hasModifiers) {
            setIsModifierModalOpen(true);
        } else {
            // If no modifiers, add directly to cart using Redux
            addToCart(restaurant);
        }
    };

    const addToCart = (item: Restaurant, modifiers?: any) => {
        // Transform Restaurant to CartItem format
        const cartItem: CartItem = {
            id: item.id,
            _id: item.id,
            name: item.name,
            cuisine: item.cuisine,
            rating: item.rating,
            image: item.image,
            deal: item.deal,
            pizzaOffer: item.pizzaOffer,
            priceOff: item.priceOff,
            price: item.price,
            discountPrice: item.discountPrice,
            active: item.active,
            userId: item.userId,
            quantity: 1,
            totalQuantity: 100, // Default value
            isFree: false,
            modifiers: modifiers || {},
            note: modifiers?.note || '',
            // Use restaurant data from TopSection (restaurantData)
            restaurantId: restaurantData?._id || '',
            restaurant: {
                id: restaurantData?._id || '',
                name: restaurantData?.name || 'Unknown Restaurant',
                image: restaurantData?.image || 'https://patronpal.com/assets/aaa.jpeg',
                businessType: restaurantData?.businessType || ''
            }
        };

        // Validate before adding to cart
        if (cartItems.length > 0) {
            const existingRestaurantId = cartItems[0].restaurant?.id || cartItems[0].restaurantId || cartItems[0].userId;
            const newRestaurantId = cartItem.restaurant?.id || cartItem.restaurantId || cartItem.userId;

            if (existingRestaurantId && newRestaurantId && existingRestaurantId !== newRestaurantId) {
                // Show warning for different restaurant
                Swal.fire({
                    title: 'Different Restaurant Selected',
                    html: `
                        <p>You can only select items from one restaurant at a time.</p>
                        <p><strong>Current cart:</strong> ${cartItems[0].restaurant?.name || 'Unknown Restaurant'}</p>
                        <p><strong>New item from:</strong> ${cartItem.restaurant?.name || 'Unknown Restaurant'}</p>
                        <p>Please complete your current restaurant order first, then go to another restaurant to select items.</p>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Go to Checkout',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#FF5C00',
                    cancelButtonColor: '#6B7280',
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Redirect to checkout page
                        navigate('/checkout');
                    }
                });
                return;
            }
        }

        // If validation passes, add item to cart
        dispatch(addItem(cartItem));
    };

    const handleAddWithModifiers = (selectedModifiers: any) => {
        addToCart(restaurant, selectedModifiers);
    };

    useEffect(() => {
        console.log('restaurantData', restaurantData)
    }, [restaurantData])


    return (
        <div className="bg-white rounded-lg">
            <div className="relative p-1.5 cursor-pointer" onClick={handleAddClick}>
                <img
                    src={restaurant.image !== 'undefined' ? restaurant.image || 'https://patronpal.com/assets/aaa.jpeg' : 'https://patronpal.com/assets/aaa.jpeg'}
                    alt={restaurant.name}
                    className="md:w-full w-72 md:h-40 h-36 object-cover rounded-lg"
                />
                <button
                    onClick={handleAddClick}
                    className="absolute bottom-4 text-primary cursor-pointer right-4 bg-white rounded-full p-2 shadow-md"
                >
                    <Plus size={20} />
                </button>
            </div>
            <div className="p-4 w-full md:text-center">
                <h3 className="font-semibold">{restaurant.name}</h3>
                <p className="text-primary font-semibold">
                    $ {restaurant.price.toFixed(2)}
                </p>
            </div>

            {hasModifiers && (
                <ModifierModal
                    isOpen={isModifierModalOpen}
                    onClose={() => setIsModifierModalOpen(false)}
                    productId={restaurant.id}
                    userId={userId}
                    productName={restaurant.name} // Pass the exact product name
                    basePrice={restaurant.price} // Pass the base price
                    onAddToOrder={handleAddWithModifiers}
                />
            )}
        </div>
    );
};

interface MenuItemsProps {
    restaurants: Restaurant[];
    userId: string;
    restaurantsByCategories: { [key: string]: Restaurant[] };
    searchQuery?: string;
    favoriteLoading?: boolean;
    isLoading?: boolean; // Add loading prop
    restaurantData?: any; // Add restaurant data prop
}

const MenuItems: React.FC<MenuItemsProps> = ({
    restaurants,
    userId,
    restaurantsByCategories,
    searchQuery = '',
    isLoading = false,
    restaurantData
}) => {
    // Add loading state handling
    if (isLoading) {
        return (
            <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading menu items...</p>
            </div>
        );
    }

    // Filter restaurants based on search query - updated to handle new structure
    const getFilteredRestaurants = (items: Restaurant[]) => {
        if (!searchQuery.trim()) return items;

        const query = searchQuery.toLowerCase();
        return items.filter(restaurant =>
            restaurant.name.toLowerCase().includes(query) ||
            restaurant.cuisine?.toLowerCase().includes(query) ||
            restaurant.parentCategory?.toLowerCase().includes(query)
        );
    };

    // Filter categories based on search query - works with categoryParents structure
    const getFilteredCategories = () => {
        if (!searchQuery.trim()) return restaurantsByCategories;

        const filteredCategories: { [key: string]: Restaurant[] } = {};

        Object.entries(restaurantsByCategories).forEach(([category, items]) => {
            const filteredItems = getFilteredRestaurants(items);
            if (filteredItems.length > 0) {
                filteredCategories[category] = filteredItems;
            }
        });

        return filteredCategories;
    };

    const filteredRestaurants = getFilteredRestaurants(restaurants);
    const filteredCategories = getFilteredCategories();

    // If searching and no results found
    if (searchQuery.trim() && filteredRestaurants.length === 0 && Object.keys(filteredCategories).length === 0) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-500 text-lg mb-2">
                    No results found for "{searchQuery}"
                </div>
                <div className="text-gray-400">
                    Try searching for different items or categories
                </div>
            </div>
        );
    }

    // Always show products grouped by subcategories when categories exist
    if (Object.keys(filteredCategories).length > 0) {
        return (
            <div className="space-y-8">
                {Object.entries(filteredCategories).map(([subcategoryName, categoryRestaurants]) => (
                    <div key={subcategoryName} className="space-y-4">
                        {/* Subcategory Header */}
                        <div className="pb-2 border-b border-gray-100">
                            <h2 className="text-xl font-semibold text-gray-800 capitalize">
                                {subcategoryName}
                            </h2>
                        </div>

                        {/* Products Grid for this subcategory */}
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {categoryRestaurants.map((restaurant) => (
                                <RestaurantCard
                                    key={restaurant.id || restaurant._id}
                                    restaurant={restaurant}
                                    userId={userId}
                                    restaurantData={restaurantData}
                                />
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        );
    }

    // Fallback to showing all restaurants in a grid if no categories but have restaurants
    if (filteredRestaurants.length > 0) {
        return (
            <div className="space-y-8">
                <div className="space-y-4">
                    <div className="pb-2 border-b border-gray-100">
                        <h2 className="text-xl font-semibold text-gray-800 capitalize">
                            All Items
                        </h2>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        {filteredRestaurants.map((restaurant) => (
                            <RestaurantCard
                                key={restaurant.id || restaurant._id}
                                restaurant={restaurant}
                                userId={userId}
                                restaurantData={restaurantData}
                            />
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    // No data state
    return (
        <div className="text-center py-12">
            <div className="text-gray-500 text-lg mb-2">
                No menu items available
            </div>
            <div className="text-gray-400">
                Please check back later
            </div>
        </div>
    );
};


export default MenuItems;
