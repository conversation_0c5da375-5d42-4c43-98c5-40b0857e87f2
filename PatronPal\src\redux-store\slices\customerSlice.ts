/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { toast } from 'react-toastify';
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";
import type {
  Customer,
  CustomerRegisterRequest,
  CustomerLoginRequest,
  CustomerLoginResponse,
  CustomerUpdateRequest,
  GoogleAuthRequest,
  PasswordResetRequest,
  OTPConfirmRequest,
  UpdatePasswordRequest,
  AddUserToCustomerRequest,
} from "../api.types";

// State interface
interface CustomerState {
  currentCustomer: Customer | null;
  customers: Customer[];
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  token: string | null;
  userName: string | null;
  authInitialized: boolean;
}

// Initial state
const initialState: CustomerState = {
  currentCustomer: null,
  customers: [],
  isAuthenticated: false,
  loading: false,
  error: null,
  token: localStorage.getItem("customerAuthToken"),
  userName: null,
  authInitialized: false,
};

// Helper function to map API response to Customer interface with safe contact handling
const mapResponseToCustomer = (response: CustomerLoginResponse): Customer => {
  const customer: Customer = {
    _id: response._id,
    fname: response.FirstName || response.fname || "",
    lname: response.LastName || response.lname || "",
    Email: response.email || response.Email || "",
    Phone: response.Phone  || "", // Handle both Phone and contact fields
    verify: response.verify || false,
    profile_pic: response.profile_pic || "",
    birthDate: response.birthDate || response.birth_date || "", // Add birth date field
    address: response.Address1
      ? `${response.Address1.street}, ${response.Address1.city}, ${response.Address1.state}, ${response.Address1.zipcode}`
      : "", // Add this line
    // Include Address1 and Address2 objects directly from response
    Address1: response.user?.Address1 || response.Address1 || undefined,
    Address2: response.user?.Address2 || response.Address2 || undefined,
  };

  console.log("Mapping API response to Customer:", {
    originalResponse: response,
    mappedCustomer: customer,
    contactValue: customer.Phone,
    Address1: customer.Address1,
    Address2: customer.Address2,
  });

  return customer;
};

// Helper function to safely parse stored customer data
const parseStoredCustomer = (customerData: string): Customer | null => {
  try {
    const parsedCustomer = JSON.parse(customerData);

    return {
      _id: parsedCustomer._id,
      fname: parsedCustomer.FirstName || parsedCustomer.fname || "",
      lname: parsedCustomer.LastName || parsedCustomer.lname || "",
      Email: parsedCustomer.email || parsedCustomer.Email || "",
      Phone: parsedCustomer.Phone || parsedCustomer.contact || "", // Handle both Phone and contact fields
      verify: parsedCustomer.verify || false,
      profile_pic: parsedCustomer.profile_pic || "",
      birthDate: parsedCustomer.birthDate || parsedCustomer.birth_date || "", // Add birth date field
      address: parsedCustomer.Address1
        ? `${parsedCustomer.Address1.street}, ${parsedCustomer.Address1.city}, ${parsedCustomer.Address1.state}, ${parsedCustomer.Address1.zipcode}`
        : "", // Add this line
      // Include Address1 and Address2 objects
      Address1: parsedCustomer.Address1 || undefined,
      Address2: parsedCustomer.Address2 || undefined,
    };
  } catch (error) {
    console.error("Error parsing stored customer data:", error);
    return null;
  }
};

// Async thunks
export const registerCustomer = createAsyncThunk<
  CustomerLoginResponse,
  CustomerRegisterRequest,
  { rejectValue: string }
>("customer/register", async (customerData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/Pregister",
      customerData
    );

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Registration failed";
    console.error("Registration error:", error);
    return rejectWithValue(message);
  }
});

export const loginCustomer = createAsyncThunk<
  CustomerLoginResponse,
  CustomerLoginRequest,
  { rejectValue: string }
>("customer/login", async (credentials, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/Plogin",
      credentials
    );

    // Store token and customer data in localStorage
    localStorage.setItem("customerAuthToken", response.data.token);
    localStorage.setItem("customer", JSON.stringify(response.data.user));

    console.log("Login API response:", response.data.user);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Login failed";
    console.error("Login error:", error);
    return rejectWithValue(message);
  }
});

export const updateCustomer = createAsyncThunk<
  CustomerLoginResponse,
  { userId: string; customerData: CustomerUpdateRequest },
  { rejectValue: string }
>("customer/update", async ({ userId, customerData }, { rejectWithValue }) => {
  try {
    console.log("Updating customer with data:", { userId, customerData });

    const response = await apiClient.put<CustomerLoginResponse>(
      `/update-profile/${userId}`,
      customerData
    );

    console.log("Update customer API response:", response.data);

    // Update localStorage with new data
    localStorage.setItem("customerAuthToken", response.data.token);
    localStorage.setItem("customer", JSON.stringify(response.data.user));

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message ||
      error.message ||
      "Failed to update customer";
    console.error("Update customer error:", error);
    return rejectWithValue(message);
  }
});

export const googleRegister = createAsyncThunk<
  CustomerLoginResponse,
  GoogleAuthRequest,
  { rejectValue: string }
>("customer/googleRegister", async (googleData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/PGoogleRegister",
      googleData
    );

    // Store token and customer data in localStorage
    localStorage.setItem("customerAuthToken", response.data.token);
    localStorage.setItem("customer", JSON.stringify(response.data.user));

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message ||
      error.message ||
      "Google registration failed";
    console.error("Google register error:", error);
    return rejectWithValue(message);
  }
});

export const googleLogin = createAsyncThunk<
  CustomerLoginResponse,
  { Email: string; googleId: string },
  { rejectValue: string }
>("customer/googleLogin", async (googleData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/PGoogleLogin",
      googleData
    );

    // Store token and customer data in localStorage
    localStorage.setItem("customerAuthToken", response.data.token);
    localStorage.setItem("customer", JSON.stringify(response.data.user));

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Google login failed";
    console.error("Google login error:", error);
    return rejectWithValue(message);
  }
});


export const sendPasswordResetOTP = createAsyncThunk<
  { message: string; success: boolean },
  PasswordResetRequest,
  { rejectValue: string }
>("customer/sendPasswordResetOTP", async (data, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      message: string;
      success: boolean;
    }>("/Pforgot-password", data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Failed to send OTP";
    console.error("Send OTP error:", error);
    return rejectWithValue(message);
  }
});

export const confirmPasswordResetOTP = createAsyncThunk<
  { message: string; success: boolean },
  OTPConfirmRequest,
  { rejectValue: string }
>("customer/confirmPasswordResetOTP", async (data, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      message: string;
      success: boolean;
    }>("/Pconfirm-otp", data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Failed to confirm OTP";
    console.error("Confirm OTP error:", error);
    return rejectWithValue(message);
  }
});

export const updatePassword = createAsyncThunk<
  { message: string; success: boolean },
  UpdatePasswordRequest,
  { rejectValue: string }
>("customer/updatePassword", async (data, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      message: string;
      success: boolean;
    }>("/Pupdate-password", data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message ||
      error.message ||
      "Failed to update password";
    console.error("Update password error:", error);
    return rejectWithValue(message);
  }
});

// Email OTP verification async thunks
export const verifyEmailOTP = createAsyncThunk<
  CustomerLoginResponse,
  { email: string; otp: string },
  { rejectValue: string }
>("customer/verifyEmailOTP", async (data, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/Pverify-email",
      data
    );

    // Store token and customer data in localStorage if verification successful
    if (response.data.token) {
      localStorage.setItem("customerAuthToken", response.data.token);
      localStorage.setItem("customer", JSON.stringify(response.data.user));
    }

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "OTP verification failed";
    console.error("Email OTP verification error:", error);
    return rejectWithValue(message);
  }
});

export const resendEmailOTP = createAsyncThunk<
  { message: string; success: boolean },
  { email: string },
  { rejectValue: string }
>("customer/resendEmailOTP", async (data, { rejectWithValue }) => {
  try {
    // Use the same registration endpoint to resend OTP
    const response = await apiClient.post<{ message: string }>("/Pregister", {
      Email: data.email,
      // Only email is needed for resending OTP to existing unverified user
    });

    return {
      message: response.data.message || "OTP sent successfully",
      success: true
    };
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Failed to resend OTP";
    console.error("Resend email OTP error:", error);
    return rejectWithValue(message);
  }
});

export const addUserToCustomer = createAsyncThunk<
  { message: string; customer: Customer; success: boolean },
  AddUserToCustomerRequest,
  { rejectValue: string }
>("customer/addUser", async (data, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{
      message: string;
      customer: Customer;
      success: boolean;
    }>("/Padd-user", data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message ||
      error.message ||
      "Failed to add user to customer";
    console.error("Add user error:", error);
    return rejectWithValue(message);
  }
});

// Add this new thunk for Apple login
export const appleLogin = createAsyncThunk<
  CustomerLoginResponse,
  AppleLoginData,
  { rejectValue: string }
>("customer/appleLogin", async (appleData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<CustomerLoginResponse>(
      "/PAppleLogin",
      appleData
    );

    // Store token and customer data in localStorage
    localStorage.setItem("customerAuthToken", response.data.token);
    localStorage.setItem("customer", JSON.stringify(response.data.user));

    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || error.message || "Apple login failed";
    console.error("Apple login error:", error);
    return rejectWithValue(message);
  }
});

// Customer slice
const customerSlice = createSlice({
  name: "customer",
  initialState,
  reducers: {
    logout: (state) => {
      state.currentCustomer = null;
      state.isAuthenticated = false;
      state.token = null;
      state.userName = null;
      state.error = null;
      localStorage.removeItem("customerAuthToken");
      // Don't remove customer data from localStorage to preserve addresses
      // localStorage.removeItem("customer");
      state.authInitialized = false;
    },
    clearError: (state) => {
      state.error = null;
    },
    setCurrentCustomer: (state, action: PayloadAction<Customer>) => {
      state.currentCustomer = action.payload;
      state.isAuthenticated = true;
    },
    initializeAuth: (state) => {
      const token = localStorage.getItem("customerAuthToken");
      const customerData = localStorage.getItem("customer");

      if (token && customerData) {
        const parsedCustomer = parseStoredCustomer(customerData);
        console.log("[parsedCustomer]", parsedCustomer);
        if (parsedCustomer) {
          state.token = token;
          state.isAuthenticated = true;
          state.currentCustomer = parsedCustomer;

          // Try to extract userName from stored data
          try {
            const rawData = JSON.parse(customerData);
            state.userName = rawData.UserName || null;
          } catch (error) {
            console.error("Error extracting userName:", error);
          }

          console.log("Auth initialized with customer:", parsedCustomer);
        } else {
          // Invalid stored data, clear it
          localStorage.removeItem("customerAuthToken");
          localStorage.removeItem("customer");
        }
      }

      state.authInitialized = true; // Add this line
    },
    validateToken: (state) => {
      const token = localStorage.getItem("customerAuthToken");
      const customerData = localStorage.getItem("customer");

      if (!token || !customerData) {
        state.currentCustomer = null;
        state.isAuthenticated = false;
        state.token = null;
        state.userName = null;
        localStorage.removeItem("customerAuthToken");
        localStorage.removeItem("customer");
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Register customer
      .addCase(registerCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(registerCustomer.fulfilled, (state) => {
        state.loading = false;
        // Registration doesn't automatically log in, just shows success
      })
      .addCase(registerCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Registration failed";
      })

      // Login customer
      .addCase(loginCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.userName = action.payload.UserName ?? null;
        state.currentCustomer = mapResponseToCustomer(action.payload);
      })
      .addCase(loginCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Login failed";
        state.isAuthenticated = false;
        state.currentCustomer = null;
        state.token = null;
        state.userName = null;
      })

      // Update customer
      .addCase(updateCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCustomer.fulfilled, (state, action) => {
        state.loading = false;
        state.token = action.payload.token;
        state.userName = action.payload.UserName ?? null;

        // Create the updated customer object with safe contact handling
        const updatedCustomer = mapResponseToCustomer(action.payload);
        state.currentCustomer = updatedCustomer;

        console.log("Customer updated successfully:", updatedCustomer);
      })
      .addCase(updateCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update customer";
      })

      // Google register
      .addCase(googleRegister.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(googleRegister.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.userName = action.payload.UserName ?? null;
        state.currentCustomer = mapResponseToCustomer(action.payload);
      })
      .addCase(googleRegister.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Google registration failed";
      })

      // Google login
      .addCase(googleLogin.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(googleLogin.fulfilled, (state, action) => {
        state.loading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.userName = action.payload.UserName ?? null;
        state.currentCustomer = mapResponseToCustomer(action.payload);
      })
      .addCase(googleLogin.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Google login failed";
      })

      // Send password reset OTP
      .addCase(sendPasswordResetOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(sendPasswordResetOTP.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(sendPasswordResetOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to send OTP";
      })

      // Confirm password reset OTP
      .addCase(confirmPasswordResetOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(confirmPasswordResetOTP.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(confirmPasswordResetOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to confirm OTP";
      })

      // Update password
      .addCase(updatePassword.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePassword.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(updatePassword.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update password";
      })

      // Add user to customer
      .addCase(addUserToCustomer.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addUserToCustomer.fulfilled, (state, action) => {
        state.loading = false;
        // Update current customer if it matches
        if (
          state.currentCustomer &&
          state.currentCustomer._id === action.payload.customer._id
        ) {
          // Ensure contact field is properly handled
          state.currentCustomer = {
            ...action.payload.customer,
            Phone: action.payload.customer.Phone || "",
          };
        }
      })
      .addCase(addUserToCustomer.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to add user to customer";
      })

      // Verify email OTP
      .addCase(verifyEmailOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(verifyEmailOTP.fulfilled, (state, _action) => {
        state.loading = false;
        // Email verification successful - user is now verified but not automatically logged in
        // The login will happen after redirect to login page
      })
      .addCase(verifyEmailOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Email verification failed";
      })

      // Resend email OTP
      .addCase(resendEmailOTP.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resendEmailOTP.fulfilled, (state) => {
        state.loading = false;
      })
      .addCase(resendEmailOTP.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to resend OTP";
      });
  },
});


// Types
interface AppleSignUpData {
  Email: string;
  appleId: string;
  customToken: string;
  keyId: string;
  firstName?: string;
  lastName?: string;
  name?: string;
}

interface AppleSignUpResponse {
  success: boolean;
  message: string;
  data?: {
    user: {
      id: string;
      email: string;
      name: string;
      appleId: string;
      isVerified: boolean;
    };
    token: string;
  };
}

interface AppleSignUpState {
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  user: any | null;
  token: string | null;
}

interface AppleLoginData {
  Email: string;
  appleId: string;
  customToken: string;
  keyId: string;
  firstName?: string;
  lastName?: string;
}

// Get base URL from environment
const getBaseUrl = (): string => {
  if (process.env.NODE_ENV === 'development') {
    return process.env.REACT_APP_DEV_BASE_URL || 'https://dev.patronworks.net/';
  }
  return process.env.REACT_APP_BASE_URL || 'http://www.patronworks.net/';
};

// Apple Sign-Up Async Thunk
export const appleSignUp = createAsyncThunk<
  AppleSignUpResponse,
  AppleSignUpData,
  { rejectValue: string }
>(
  'auth/appleSignUp',
  async (appleSignUpData, { rejectWithValue }) => {
    try {
      const baseUrl = getBaseUrl();
      const response = await fetch(`${baseUrl}api/auth/apple-signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: JSON.stringify(appleSignUpData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Apple sign-up failed');
      }

      if (data.success) {
        // Store token in localStorage
        if (data.data?.token) {
          localStorage.setItem('authToken', data.data.token);
          localStorage.setItem('user', JSON.stringify(data.data.user));
        }
        
        toast.success(data.message || 'Successfully signed up with Apple!');
        return data;
      } else {
        throw new Error(data.message || 'Apple sign-up failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Apple sign-up failed';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Apple Sign-Up Slice
const appleSignUpSlice = createSlice({
  name: 'appleAuth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearAuth: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.error = null;
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
    },
    initializeAuth: (state) => {
      const token = localStorage.getItem('authToken');
      const user = localStorage.getItem('user');
      
      if (token && user) {
        try {
          state.token = token;
          state.isAuthenticated = true;
        } catch (error) {
          // Clear invalid data
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
        }
      }
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Apple Sign-Up
      .addCase(appleSignUp.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(appleSignUp.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
        state.isAuthenticated = true;
        // state.user = action.payload.data?.user || null;
        state.token = action.payload.data?.token || null;
      })
      .addCase(appleSignUp.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || 'Apple sign-up failed';
        state.isAuthenticated = false;
        // state.user = null;
        state.token = null;
      });
  },
});

// Export actions
export const { 
  // clearError, 
  clearAuth, 
  // initializeAuth, 
  setLoading 
} = appleSignUpSlice.actions;

// Export selectors
export const selectAppleLoading = (state: { appleAuth: AppleSignUpState }) => state.appleAuth.isLoading;
export const selectAppleError = (state: { appleAuth: AppleSignUpState }) => state.appleAuth.error;
export const selectAppleIsAuthenticated = (state: { appleAuth: AppleSignUpState }) => state.appleAuth.isAuthenticated;
export const selectAppleUser = (state: { appleAuth: AppleSignUpState }) => state.appleAuth.user;
export const selectAppleToken = (state: { appleAuth: AppleSignUpState }) => state.appleAuth.token;


// Export actions
export const {
  logout,
  clearError,
  setCurrentCustomer,
  initializeAuth,
  validateToken,
} = customerSlice.actions;

// Selectors
export const selectCurrentCustomer = (state: { customer: CustomerState }) =>
  state.customer.currentCustomer;
export const selectCustomers = (state: { customer: CustomerState }) =>
  state.customer.customers;
export const selectIsAuthenticated = (state: { customer: CustomerState }) =>
  state.customer.isAuthenticated;
export const selectCustomerLoading = (state: { customer: CustomerState }) =>
  state.customer.loading;
export const selectCustomerError = (state: { customer: CustomerState }) =>
  state.customer.error;
export const selectCustomerToken = (state: { customer: CustomerState }) =>
  state.customer.token;
export const selectUserName = (state: { customer: CustomerState }) =>
  state.customer.userName;

export const selectAuthInitialized = (state: { customer: CustomerState }) =>
  state.customer.authInitialized;

// Helper function to safely get contact from current customer
export const selectSafeCustomerContact = (state: {
  customer: CustomerState;
}): string => {
  return state.customer.currentCustomer?.Phone || "";
};

// Export reducer
export default customerSlice.reducer;
