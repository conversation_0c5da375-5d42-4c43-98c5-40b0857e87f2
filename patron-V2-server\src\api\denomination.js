import BillDenomination from '../models/denomination.js';
// API to get All bill denominations
export const getBillDenomination = async (req, res) => {
  try {
    const billDenominations = await BillDenomination.find();
    res.json(billDenominations);
  } catch (err) {
    res.status(500).json({ message: 'Error retrieving bill denominations', error: err });
  }
};

// API to get bill denominations by userId
export const getBillDenominationsByUserId = async (req, res) => {
  try {
    const { userId } = req.query.userId;
    const billDenominations = await BillDenomination.find({ userId });
    if (billDenominations.length === 0) {
      return res.status(404).json({ message: 'No bill denominations found for this user' });
    }
    res.json(billDenominations);
  } catch (err) {
    res.status(500).json({ message: 'Error retrieving bill denominations by userId', error: err });
  }
};


// API to get a bill denomination by ID
export const getBillDenominationById = async (req, res) => {
  try {
    const billDenomination = await BillDenomination.findById(req.params._id);
    res.json(billDenomination);
  } catch (err) {
    res.status(500).json({ message: 'Error retrieving bill denomination', error: err });
  }
};


export const postBillDenomination = async (req, res) => {
  try {
    const {
      denominations,
      totalCashOnhand,
      EnteredBy,
      userId,
      type,
      note,
      overShortAmount
    } = req.body;

    // Get today's date range (00:00 to 23:59)
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    // Check if a record already exists for this user, type, and date
    const existingRecord = await BillDenomination.findOne({
      userId,
      type,
      updatedAt: {
        $gte: startOfDay,
        $lte: endOfDay
      }
    });

    if (existingRecord) {
      // Merge denominations (by value)
      denominations.forEach(newDenom => {
        const existingDenom = existingRecord.denominations.find(d => d.value === newDenom.value);
        if (existingDenom) {
          existingDenom.quantity += newDenom.quantity;
        } else {
          existingRecord.denominations.push(newDenom);
        }
      });

      // Update totals
      existingRecord.totalCashOnhand += totalCashOnhand;
      existingRecord.overShortAmount += overShortAmount;
      existingRecord.note = note || existingRecord.note;

      await existingRecord.save();

      return res.status(200).json({ message: 'Bill denomination updated successfully', data: existingRecord });
    } else {
      // Create new entry
      const newBill = new BillDenomination({
        denominations,
        totalCashOnhand,
        EnteredBy,
        userId,
        type,
        note,
        overShortAmount
      });

      await newBill.save();

      return res.status(201).json({ message: 'Bill denomination created successfully', data: newBill });
    }
  } catch (err) {
    console.error("Error in postBillDenomination:", err);
    res.status(500).json({ message: 'Error creating or updating bill denomination entry', error: err });
  }
};


// export const postBillDenomination = async (req, res) => {
//   try {
//     const { denominations, totalCashOnhand, EnteredBy, userId, type, note, overShortAmount } = req.body;

//     const newBill = new BillDenomination({
//       denominations,
//       totalCashOnhand,
//       EnteredBy,
//       userId,
//       type,
//       note,
//       overShortAmount
//     });

//     await newBill.save();
//     res.status(201).json(newBill);
//   } catch (err) {
//     res.status(500).json({ message: 'Error creating bill denomination entry', error: err });
//   }
// };

  // export const postBillDenomination = async (req, res) => {
  //   try {
  //     const { denominations, totalCashOnhand,EnteredBy , userId  } = req.body;

  //     const newBillDenomination = new BillDenomination({ denominations,
  //       totalCashOnhand,
  //       EnteredBy,
  //       userId
  //     });
  //     await newBillDenomination.save();
  //     res.status(201).json(newBillDenomination);
  //   } catch (err) {
  //     res.status(500).json({ message: 'Error creating bill denomination', error: err });
  //   }
  // };
export const getCashDrawerReport = async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ message: "User ID is required" });
    }

    const allEntries = await BillDenomination.find({ userId }).sort({ createdAt: 1 });

    const dailySummary = {};
    const allEntriesByDate = {};

    allEntries.forEach(entry => {
      const dateKey = new Date(entry.createdAt).toISOString().split('T')[0]; // YYYY-MM-DD

      // Initialize if not exists
      if (!dailySummary[dateKey]) {
        dailySummary[dateKey] = {
          date: dateKey,
          totalCashIn: 0,
          totalCashOut: 0,
          totalDrop: 0,
          totalPayout: 0,
          overShort: 0,
          openingCash:0,
        };
        allEntries[dateKey] = [];
      }

      // Update daily summary
      if (entry.type === 'cash_in') dailySummary[dateKey].totalCashIn += entry.totalCashOnhand;
      else if (entry.type === 'cash_out') dailySummary[dateKey].totalCashOut += entry.totalCashOnhand;
      else if (entry.type === 'drop') dailySummary[dateKey].totalDrop += entry.totalCashOnhand;
      else if (entry.type === 'payout') dailySummary[dateKey].totalPayout += entry.totalCashOnhand;
      else if (entry.type === 'opening_cash') {dailySummary[dateKey].openingCash += entry.totalCashOnhand;}

      dailySummary[dateKey].overShort += entry.overShortAmount || 0;

      // Store entry
      allEntries[dateKey].push(entry);
    });

    res.json({
      summary: Object.values(dailySummary), // Array of summaries by date
      allEntries // All entries organized by date
    });

  } catch (err) {
    res.status(500).json({ message: 'Error generating daily cash drawer summary', error: err });
  }
};


// export const getCashDrawerReport = async (req, res) => {
//   try {
//     const { userId } = req.query;
//     const allEntries = await BillDenomination.find({ userId });

//     const summary = {
//       totalCashIn: 0,
//       totalCashOut: 0,
//       totalDrop: 0,
//       totalPayout: 0,
//       overShort: 0,
//     };

//     allEntries.forEach(entry => {
//       if (entry.type === 'cash_in') summary.totalCashIn += entry.totalCashOnhand;
//       else if (entry.type === 'cash_out') summary.totalCashOut += entry.totalCashOnhand;
//       else if (entry.type === 'drop') summary.totalDrop += entry.totalCashOnhand;
//       else if (entry.type === 'payout') summary.totalPayout += entry.totalCashOnhand;

//       summary.overShort += entry.overShortAmount;
//     });

//     res.json({ summary, allEntries });
//   } catch (err) {
//     res.status(500).json({ message: 'Error generating cash drawer report', error: err });
//   }
// };

export const getEntriesByType = async (req, res) => {
  try {
    const { userId, type } = req.query;
    const entries = await BillDenomination.find({ userId, type });
    res.json(entries);
  } catch (err) {
    res.status(500).json({ message: 'Error fetching entries by type', error: err });
  }
};


export const updateBillDenomination = async (req, res) => {
  try {
    const updatedres = await BillDenomination.findByIdAndUpdate(
      { _id: req.params._id },
      {
        $set: { ...req.body },
        $currentDate: { updatedAt: true }
      },
      { new: true } // Return the updated document
    );

    if (updatedres) {
      res.status(200).json({ message: 'Data updated successfully', updatedres });
    } else {
      res.status(404).json({ message: 'Bill denomination not found' });
    }
  } catch (err) {
    res.status(500).json({ message: 'Error updating data', error: err });
  }
};

// API to delete a bill denomination by ID
export const deleteBillDenomination = async (req, res) => {
  try {
    const billDenominationId = req.params._id;
    // Find the bill denomination by ID and delete it
    const deletedBillDenomination = await BillDenomination.findByIdAndDelete(billDenominationId);
    if (!deletedBillDenomination) {
      return res.status(404).json({ message: 'Bill denomination not found' });
    }
    res.json({ message: 'Bill denomination deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: 'Error deleting bill denomination', error: err });
  }
};


