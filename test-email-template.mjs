import { generateOrderReadyEmailTemplate } from './patron-V2-server/src/templates/orderReadyEmailTemplate.js';
import fs from 'fs';

// Test data
const testData = {
  customerName: '<PERSON>',
  orderType: 'pickup',
  orderNo: 'ORD-12345',
  businessName: 'Test Restaurant',
  deviceInfo: {
    _id: '507f1f77bcf86cd799439011',
    name: 'Test Restaurant'
  },
  apiBaseUrl: 'http://localhost:4444',
  orderId: '507f1f77bcf86cd799439012',
  customerId: '507f1f77bcf86cd799439013',
  userId: '507f1f77bcf86cd799439014'
};

console.log('Generating email template with test data...');
console.log('Test data:', testData);

try {
  const emailHtml = generateOrderReadyEmailTemplate(
    testData.customerName,
    testData.orderType,
    testData.orderNo,
    testData.businessName,
    testData.deviceInfo,
    testData.apiBaseUrl,
    testData.orderId,
    testData.customerId,
    testData.userId
  );

  // Save the generated HTML to a file for testing
  fs.writeFileSync('test-email-output.html', emailHtml);
  console.log('✅ Email template generated successfully!');
  console.log('📄 Saved to test-email-output.html');
  
  // Check if the review link is present with required parameters
  const hasReviewLink = emailHtml.includes('Leave a Review');
  const hasDeviceIdParam = emailHtml.includes(`deviceId=${encodeURIComponent(testData.deviceInfo._id)}`);
  const hasCustomerIdParam = emailHtml.includes(`customerId=${encodeURIComponent(testData.customerId)}`);
  const hasOrderIdParam = emailHtml.includes(`orderId=${encodeURIComponent(testData.orderId)}`);

  console.log('\n🔍 Review link validation:');
  console.log(`Review link present: ${hasReviewLink ? '✅' : '❌'}`);
  console.log(`Device ID parameter: ${hasDeviceIdParam ? '✅' : '❌'}`);
  console.log(`Customer ID parameter: ${hasCustomerIdParam ? '✅' : '❌'}`);
  console.log(`Order ID parameter: ${hasOrderIdParam ? '✅' : '❌'}`);

  if (hasReviewLink && hasDeviceIdParam && hasCustomerIdParam && hasOrderIdParam) {
    console.log('\n🎉 Review link is properly configured!');
  } else {
    console.log('\n⚠️  Review link is missing some parameters.');
  }

} catch (error) {
  console.error('❌ Error generating email template:', error);
}
