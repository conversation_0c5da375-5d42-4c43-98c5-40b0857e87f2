import { generateOrderReadyEmailTemplate } from './patron-V2-server/src/templates/orderReadyEmailTemplate.js';
import fs from 'fs';

// Test data
const testData = {
  customerName: '<PERSON>',
  orderType: 'pickup',
  orderNo: 'ORD-12345',
  businessName: 'Test Restaurant',
  deviceInfo: {
    _id: '507f1f77bcf86cd799439011',
    name: 'Test Restaurant'
  },
  apiBaseUrl: 'http://localhost:4444',
  orderId: '507f1f77bcf86cd799439012',
  customerId: '507f1f77bcf86cd799439013',
  userId: '507f1f77bcf86cd799439014'
};

console.log('Generating email template with test data...');
console.log('Test data:', testData);

try {
  const emailHtml = generateOrderReadyEmailTemplate(
    testData.customerName,
    testData.orderType,
    testData.orderNo,
    testData.businessName,
    testData.deviceInfo,
    testData.apiBaseUrl,
    testData.orderId,
    testData.customerId,
    testData.userId
  );

  // Save the generated HTML to a file for testing
  fs.writeFileSync('test-email-output.html', emailHtml);
  console.log('✅ Email template generated successfully!');
  console.log('📄 Saved to test-email-output.html');
  
  // Check if the required fields are present
  const hasDeviceId = emailHtml.includes(`value="${testData.deviceInfo._id}"`);
  const hasCustomerId = emailHtml.includes(`value="${testData.customerId}"`);
  const hasOrderId = emailHtml.includes(`value="${testData.orderId}"`);
  
  console.log('\n🔍 Field validation:');
  console.log(`Device ID present: ${hasDeviceId ? '✅' : '❌'}`);
  console.log(`Customer ID present: ${hasCustomerId ? '✅' : '❌'}`);
  console.log(`Order ID present: ${hasOrderId ? '✅' : '❌'}`);
  
  if (hasDeviceId && hasCustomerId && hasOrderId) {
    console.log('\n🎉 All required fields are present in the template!');
  } else {
    console.log('\n⚠️  Some required fields are missing from the template.');
  }

} catch (error) {
  console.error('❌ Error generating email template:', error);
}
