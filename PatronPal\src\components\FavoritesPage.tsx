/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import { assets } from "../assets/assets";
import { 
  getFavoriteByCustomerId, 
  addFavorite, 
  clearFavoriteDevices,
  selectFavoriteDevices,
  selectFavoriteLoading,
  selectDeviceError 
} from "../redux-store/slices/deviceSlice";
import { handleRestaurantTaxData } from "../utils/taxUtils";

// You'll need to import your auth slice to get current user
// import { selectCurrentUser } from "../redux/slices/authSlice";

interface Device {
  _id: string;
  name: string;
  active: boolean;
  userId: any;
  Line1: string;
  Line2?: string;
  City: string;
  Phoneno: string;
  State: string;
  PostalCode: string;
  Country: string;
  image?: string;
  delivery?: boolean;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  businessType?: string;
  Streetaddress?: string;
  reviews?: Array<{
    _id?: string;
    food: number;
    service: number;
    ambiance: number;
    testimonial: string;
    customerId: any;
    averageScore?: number;
  }>;
  favorites?: Array<{
    _id?: string;
    customerId: any;
  }>;
}

export default function FavoritesPage() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const favoriteDevices = useSelector(selectFavoriteDevices);
    const loading = useSelector(selectFavoriteLoading);
    const error = useSelector(selectDeviceError);
    
    // Get the current user from your auth state
    const userId = useSelector((state: any) => state.customer?.currentCustomer?._id);
    
    const [refreshing, setRefreshing] = useState(false);
    const [navigatingToRestaurant, setNavigatingToRestaurant] = useState<string | null>(null);
    const [pageLoading, setPageLoading] = useState(true);

    useEffect(() => {
        const loadFavorites = async () => {
            setPageLoading(true);
            if (userId) {
                await dispatch(getFavoriteByCustomerId(userId) as any);
            }
            // Add a minimum loading time for better UX
            setTimeout(() => {
                setPageLoading(false);
            }, 1000);
        };

        loadFavorites();

        // Cleanup on unmount
        return () => {
            dispatch(clearFavoriteDevices());
        };
    }, [dispatch, userId]);

    const handleRemoveFavorite = async (deviceId: string) => {
        if (!userId) return;
        
        try {
            setRefreshing(true);
            await dispatch(addFavorite({ 
                deviceId, 
                customerId: userId 
            }) as any);
            
            // Refresh the favorites list
            dispatch(getFavoriteByCustomerId(userId) as any);
        } catch (error) {
            console.error('Error removing favorite:', error);
        } finally {
            setRefreshing(false);
        }
    };

    const handleRestaurantClick = async (device: Device) => {
        setNavigatingToRestaurant(device._id);

        // Store userId and fetch tax data using utility function
        if (device.userId) {
            try {
                await handleRestaurantTaxData(device.userId);
            } catch (error) {
                console.error('Error handling restaurant tax data:', error);
            }
        }

        // Navigate to product page with device ID and user ID
        // Format: product/{deviceId}/{userId}
        setTimeout(() => {
            navigate(`/product/${device._id}/${device.userId}`);
        }, 500);
    };

    const calculateAverageRating = (reviews: Device['reviews'] = []) => {
        if (!reviews.length) return "0";

        // Only use reviews that have the new rating field (from email reviews)
        const validReviews = reviews.filter(review => review.rating && review.rating > 0);

        if (validReviews.length === 0) return "0";

        const totalScore = validReviews.reduce((sum, review) => sum + review.rating!, 0);

        return (totalScore / validReviews.length).toFixed(1);
    };

    const getDeliveryTime = (device: Device) => {
        if (device.deliveryStartTime && device.deliveryEndTime) {
            return `${device.deliveryStartTime} - ${device.deliveryEndTime}`;
        }
        return "25-35 min"; // Default fallback
    };

    const getDeliveryFee = (device: Device) => {
        return device.ChargesperKm ? (device.ChargesperKm / 100).toFixed(2) : "2.99";
    };

    // Show page loading when first loading
    if (pageLoading) {
        return (
            <div className="flex items-center justify-center h-screen bg-gray-50">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
                    <p className="mt-4 text-gray-600">Loading your favorites...</p>
                </div>
            </div>
        );
    }

    // Show loading for subsequent operations
    if (loading && !refreshing && !pageLoading) {
        return (
            <div className="block min-h-screen bg-gray-50 w-full">
                <div className="max-w-2xl mx-auto p-4 w-full">
                    <div className="flex justify-center items-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        <span className="ml-2 text-gray-600">Loading favorites...</span>
                    </div>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="block min-h-screen bg-gray-50 w-full">
                <div className="max-w-2xl mx-auto p-4 w-full">
                    <div className="text-center py-8 text-red-500">
                        <p>Error loading favorites: {error}</p>
                        <button 
                            onClick={() => userId && dispatch(getFavoriteByCustomerId(userId) as any)}
                            className="mt-2 px-4 py-2 bg-primary text-white rounded-lg hover:opacity-90"
                        >
                            Retry
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="block min-h-screen bg-gray-50 w-full">
            <div className="max-w-2xl mx-auto p-4 w-full">
                {/* Header */}
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">My Favorites</h2>
                    {favoriteDevices.length > 0 && (
                        <button
                            onClick={() => userId && dispatch(getFavoriteByCustomerId(userId) as any)}
                            disabled={refreshing}
                            className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 disabled:opacity-50"
                        >
                            {refreshing ? 'Refreshing...' : 'Refresh'}
                        </button>
                    )}
                </div>

                {/* Loading overlay for navigation */}
                {navigatingToRestaurant && (
                    <div className="fixed inset-0 bg-white bg-opacity-50 flex items-center justify-center z-50">
                        <div className="bg-white rounded-lg p-6 text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                            <p className="mt-2 text-gray-600">Opening restaurant...</p>
                        </div>
                    </div>
                )}

                {/* Favorite Restaurants Section */}
                <div className="space-y-6">
                    {favoriteDevices.length > 0 ? (
                        favoriteDevices.map(device => (
                            <RestaurantCard
                                key={device._id}
                                device={device}
                                onRemove={() => handleRemoveFavorite(device._id)}
                                onRestaurantClick={() => handleRestaurantClick(device)}
                                averageRating={calculateAverageRating(device.reviews)}
                                deliveryTime={getDeliveryTime(device)}
                                deliveryFee={getDeliveryFee(device)}
                                isRemoving={refreshing}
                                isNavigating={navigatingToRestaurant === device._id}
                            />
                        ))
                    ) : (
                        <div className="text-center py-12 text-gray-500">
                            <HeartIcon />
                            <p className="mt-4 text-lg font-medium">No favorite restaurants yet</p>
                            <p className="text-sm mt-2">Explore restaurants and add them to your favorites</p>
                            <Link 
                                to="/all-restaurants" 
                                className="inline-block mt-4 px-6 py-2 bg-primary text-white rounded-lg hover:opacity-90"
                            >
                                Browse Restaurants
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

interface RestaurantCardProps {
    device: Device;
    onRemove: () => void;
    onRestaurantClick: () => void;
    averageRating: string;
    deliveryTime: string;
    deliveryFee: string;
    isRemoving: boolean;
    isNavigating: boolean;
}

function RestaurantCard({ 
    device, 
    onRemove, 
    onRestaurantClick,
    averageRating, 
    deliveryTime, 
    deliveryFee,
    isRemoving,
    isNavigating
}: RestaurantCardProps) {
    const getBusinessCategory = () => {
        return device.businessType || "Restaurant";
    };

    const getImageUrl = () => {
        return device.image || assets.order_history;
    };

    const getFullAddress = () => {
        const parts = [device.Line1, device.Line2, device.City, device.State].filter(Boolean);
        return parts.join(', ');
    };

    return (
        <div className="relative">
            <div 
                className={`flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-4 border border-gray-200 bg-white rounded-2xl shadow-sm hover:shadow-md transition-all cursor-pointer ${isNavigating ? 'opacity-50' : ''}`}
                onClick={onRestaurantClick}
            >
                <div className="relative w-full sm:w-auto">
                    <img
                        src={getImageUrl()}
                        alt={device.name}
                        className="w-full sm:w-32 h-32 rounded-lg object-cover"
                    />
                    <button 
                        onClick={(e) => {
                            e.stopPropagation();
                            onRemove();
                        }}
                        disabled={isRemoving || isNavigating}
                        className="absolute top-2 right-2 bg-white rounded-full p-2 shadow-md hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isRemoving ? (
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                        ) : (
                            <HeartFilledIcon />
                        )}
                    </button>
                </div>

                <div className="flex-1 w-full">
                    <div className="flex flex-col sm:flex-row justify-between items-start">
                        <div className="flex-1">
                            <h3 className="text-xl font-semibold text-gray-900">{device.name}</h3>
                            <p className="text-sm text-gray-600 mt-1">{getFullAddress()}</p>
                            
                            <div className="flex items-center space-x-2 mt-2">
                                <div className="flex items-center">
                                    <StarIcon />
                                    <span className="text-sm text-gray-600 ml-1">
                                        {averageRating !== "0" ? averageRating : "New"}
                                    </span>
                                </div>
                                <span className="text-gray-400">•</span>
                                <span className="text-sm text-gray-600">{getBusinessCategory()}</span>
                            </div>
                            
                            <div className="flex items-center space-x-4 mt-2">
                                <span className="text-sm text-gray-600">{deliveryTime}</span>
                                {device.delivery && (
                                    <>
                                        <span className="text-gray-400">•</span>
                                        <span className="text-sm text-gray-600">
                                            ${deliveryFee} delivery
                                        </span>
                                    </>
                                )}
                                {!device.active && (
                                    <>
                                        <span className="text-gray-400">•</span>
                                        <span className="text-sm text-red-600">Currently Closed</span>
                                    </>
                                )}
                            </div>

                            {device.Phoneno && (
                                <div className="mt-2">
                                    <span className="text-sm text-gray-600">📞 {device.Phoneno}</span>
                                </div>
                            )}
                            
                            {/* Click to view indicator */}
                            <div className="mt-3">
                                <span className="text-sm text-primary font-medium">
                                    {isNavigating ? 'Opening...' : 'Click to view restaurant'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Loading indicator for this specific card */}
            {isNavigating && (
                <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-2xl">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
            )}
        </div>
    );
}

function HeartIcon() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-gray-400 mx-auto"
        >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
    );
}

function HeartFilledIcon() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="text-orange-500"
        >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
        </svg>
    );
}

function StarIcon() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="text-orange-500"
        >
            <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"></polygon>
        </svg>
    );
}