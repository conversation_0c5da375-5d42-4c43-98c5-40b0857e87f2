import React from 'react';

interface CuisineCategory {
  name: string;
  image: string;
  categoryId?: string;
}

interface CuisineCardProps {
  category: CuisineCategory;
}

const CuisineCard: React.FC<CuisineCardProps> = ({ category }) => {
  return (
    <button
      className="flex flex-col items-center"
    >
      <img
        src={category?.image || 'https://patronpal.com/assets/beverage.jpeg'}
        alt={category.name}
        className="w-28 h-28 object-cover rounded-lg"
      />
      <div className="text-xs mt-1 text-center text-primary">{category.name}</div>
    </button>
  );
};

export default CuisineCard;