import React, { useState } from 'react';

interface TabOption {
  id: string;
  label: string;
  isComingSoon?: boolean;
}

interface DeliveryTabsProps {
  onTabChange?: (tabId: string) => void;
}


const Topbar: React.FC<DeliveryTabsProps> = ({ onTabChange }) => {

  
  const tabs: TabOption[] = [
    { id: 'delivery', label: 'Delivery' },
    { id: 'pickup', label: 'Pick Up' },
    { id: 'stores', label: 'Stores' },
    { id: 'comingSoon', label: 'Coming Soon', isComingSoon: true },
  ];

  const [activeTab, setActiveTab] = useState<string>(tabs[0].id);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  return (
    <div className="w-full md:border-b border-none border-gray-200">
      <div className="flex items-center">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            onClick={() => !tab.isComingSoon && handleTabClick(tab.id)}
            className={`relative px-4 py-3 cursor-pointer ${
              tab.isComingSoon ? 'cursor-default' : ''
            }`}
          >
            {tab.isComingSoon ? (
              <span className="px-4 py-1 text-sm text-primary border border-primary rounded-full">
                {tab.label}
              </span>
            ) : (
              <>
                <span
                  className={`text-sm font-medium ${
                    activeTab === tab.id ? 'text-black' : 'text-gray-500'
                  }`}
                >
                  {tab.label}
                </span>
                {activeTab === tab.id && (
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary"></div>
                )}
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Topbar
