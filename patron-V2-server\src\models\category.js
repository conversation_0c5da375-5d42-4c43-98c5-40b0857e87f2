import mongoose from 'mongoose';
const categorySchema = new mongoose.Schema({

    name: {
        type: String
    },
    extraData: {
        type: String
    },
    category_pic:{
        type:String
    },
    categoryType: {
        type: String
    },
    userId:{
        type:mongoose.Schema.Types.ObjectId,
        required:true,
        ref:'user'
    },
    order: {
        type: Number
    },
    hasPicture: {
        type: String
    },
    active: {
        type: String
    },
   
    parentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref:'parentcategory'
    },
    lampixIcon: {
        type: String
    },
    translation: {
        type: String
    },
    showPictures: {
        type: Boolean
    },
    
    creatdateFormat: {
        type: Date,
        default: Date.now
    }
}) 
const category = mongoose.model("category", categorySchema);
export default category;