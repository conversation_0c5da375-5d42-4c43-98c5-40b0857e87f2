/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";
// import axios from "axios";

// Modifier interfaces
interface ModifierProperty {
  name: string;
  totalQuantity: number;
  price: number;
}

interface ModifierItem {
  name: string;
  properties: ModifierProperty[];
}

interface Modifier {
  _id: string;
  Modifier: ModifierItem[];
  isActive: boolean;
  productId:
    | string
    | {
        _id: string;
        name?: string;
        [key: string]: any;
      };
  userId: string;
  __v?: number;
}

// Request interfaces
interface ModifierCreateRequest {
  Modifier: ModifierItem[];
  productId: string;
  userId: string;
  isActive: boolean;
}

interface ModifierUpdateRequest {
  Modifier: ModifierItem[];
  productId: string;
  userId: string;
  isActive: boolean;
}

interface ModifierFilters {
  userId?: string;
  productId?: string;
}

// State interface
interface ModifierState {
  modifiers: Modifier[];
  currentModifier: Modifier | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: ModifierState = {
  modifiers: [],
  currentModifier: null,
  loading: false,
  error: null,
};

// Async thunks
export const getModifiers = createAsyncThunk<
  Modifier[],
  ModifierFilters,
  { rejectValue: string }
>("modifier/getModifiers", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.userId) params.append("userId", filters.userId);
    if (filters.productId) params.append("productId", filters.productId);

    console.log(`Fetching modifiers with params: ${params.toString()}`);

    // Make sure this URL matches your backend route
    const response = await apiClient.get<Modifier[]>(
      `/modifiers/?${params.toString()}`
    );

    console.log("[getModifiers] response:", response.data);
    return response.data;
  } catch (error: any) {
    console.error("[getModifiers] error:", error);
    const message =
      error.response?.data?.message || "Failed to fetch modifiers";
    return rejectWithValue(message);
  }
});

export const getModifierById = createAsyncThunk<
  Modifier,
  string,
  { rejectValue: string }
>("modifier/getModifierById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Modifier>(`/modifiers/${id}`);
    console.log("[getModifierById] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch modifier";
    return rejectWithValue(message);
  }
});

export const createModifier = createAsyncThunk<
  Modifier,
  ModifierCreateRequest,
  { rejectValue: string }
>("modifier/createModifier", async (modifierData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<Modifier>("/modifiers", modifierData);
    console.log("[createModifier] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to create modifier";
    return rejectWithValue(message);
  }
});

export const updateModifier = createAsyncThunk<
  Modifier,
  { id: string; modifierData: ModifierUpdateRequest },
  { rejectValue: string }
>(
  "modifier/updateModifier",
  async ({ id, modifierData }, { rejectWithValue }) => {
    try {
      const response = await apiClient.put<{
        populatedModifier?: Modifier;
        message: string;
        category?: Modifier;
      }>(`/modifier/${id}`, modifierData);

      console.log("[updateModifier] response:", response.data);

      // Handle different response structures based on your controller
      if (response.data.populatedModifier) {
        return response.data.populatedModifier;
      } else if (response.data.category) {
        return response.data.category;
      } else {
        // If the API only returns a success message, fetch the updated modifier
        const updatedModifierResponse = await apiClient.get<Modifier>(
          `/modifiers/${id}`
        );
        return updatedModifierResponse.data;
      }
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to update modifier";
      return rejectWithValue(message);
    }
  }
);

export const putModifier = createAsyncThunk<
  Modifier,
  { id: string; name: string; properties: ModifierProperty[] },
  { rejectValue: string }
>(
  "modifier/putModifier",
  async ({ id, name, properties }, { rejectWithValue }) => {
    try {
      const response = await apiClient.put<Modifier>(`/modifiers/${id}`, {
        name,
        properties,
      });
      console.log("[putModifier] response:", response.data);
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to update modifier";
      return rejectWithValue(message);
    }
  }
);

export const deleteModifier = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("modifier/deleteModifier", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete(`/modifiers/${id}`);
    console.log("[deleteModifier] Modifier deleted successfully");
    return id; // Return the deleted modifier ID
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to delete modifier";
    return rejectWithValue(message);
  }
});

// Modifier slice
const modifierSlice = createSlice({
  name: "modifier",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentModifier: (state, action: PayloadAction<Modifier>) => {
      state.currentModifier = action.payload;
    },
    clearCurrentModifier: (state) => {
      state.currentModifier = null;
    },
    // Additional utility reducers
    updateModifierInList: (state, action: PayloadAction<Modifier>) => {
      const index = state.modifiers.findIndex(
        (m) => m._id === action.payload._id
      );
      if (index !== -1) {
        state.modifiers[index] = action.payload;
      }
    },
    removeModifierFromList: (state, action: PayloadAction<string>) => {
      state.modifiers = state.modifiers.filter((m) => m._id !== action.payload);
    },
  },
  extraReducers: (builder) => {
    builder
      // Get modifiers
      .addCase(getModifiers.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getModifiers.fulfilled, (state, action) => {
        state.loading = false;
        state.modifiers = action.payload;
      })
      .addCase(getModifiers.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch modifiers";
      })

      // Get modifier by ID
      .addCase(getModifierById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getModifierById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentModifier = action.payload;
      })
      .addCase(getModifierById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch modifier";
      })

      // Create modifier
      .addCase(createModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createModifier.fulfilled, (state, action) => {
        state.loading = false;
        state.modifiers.push(action.payload);
      })
      .addCase(createModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create modifier";
      })

      // Update modifier (complex update)
      .addCase(updateModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateModifier.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.modifiers.findIndex(
          (m) => m._id === action.payload._id
        );
        if (index !== -1) {
          state.modifiers[index] = action.payload;
        }
        if (
          state.currentModifier &&
          state.currentModifier._id === action.payload._id
        ) {
          state.currentModifier = action.payload;
        }
      })
      .addCase(updateModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update modifier";
      })

      // Put modifier (simple update)
      .addCase(putModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(putModifier.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.modifiers.findIndex(
          (m) => m._id === action.payload._id
        );
        if (index !== -1) {
          state.modifiers[index] = action.payload;
        }
        if (
          state.currentModifier &&
          state.currentModifier._id === action.payload._id
        ) {
          state.currentModifier = action.payload;
        }
      })
      .addCase(putModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update modifier";
      })

      // Delete modifier
      .addCase(deleteModifier.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteModifier.fulfilled, (state, action) => {
        state.loading = false;
        state.modifiers = state.modifiers.filter(
          (m) => m._id !== action.payload
        );
        if (
          state.currentModifier &&
          state.currentModifier._id === action.payload
        ) {
          state.currentModifier = null;
        }
      })
      .addCase(deleteModifier.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete modifier";
      });
  },
});

// Export actions
export const {
  clearError,
  setCurrentModifier,
  clearCurrentModifier,
  updateModifierInList,
  removeModifierFromList,
} = modifierSlice.actions;

// Selectors
export const selectModifiers = (state: { modifier: ModifierState }) =>
  state.modifier.modifiers;
export const selectCurrentModifier = (state: { modifier: ModifierState }) =>
  state.modifier.currentModifier;
export const selectModifierLoading = (state: { modifier: ModifierState }) =>
  state.modifier.loading;
export const selectModifierError = (state: { modifier: ModifierState }) =>
  state.modifier.error;

// Additional selectors
export const selectModifiersByProductId =
  (productId: string) => (state: { modifier: ModifierState }) =>
    state.modifier.modifiers.filter((modifier) =>
      typeof modifier.productId === "string"
        ? modifier.productId === productId
        : modifier.productId._id === productId
    );

export const selectActiveModifiers = (state: { modifier: ModifierState }) =>
  state.modifier.modifiers.filter((modifier) => modifier.isActive);

// Export reducer
export default modifierSlice.reducer;
