
/* eslint-disable no-useless-escape */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { FcGoogle } from 'react-icons/fc';
import { FaApple } from 'react-icons/fa';
import { Eye, EyeOff } from 'lucide-react';
import type { AppDispatch } from '../../redux-store/store';
import {
    registerCustomer,
    googleRegister,
    clearError,
    selectCustomerLoading,
    selectCustomerError,
    selectIsAuthenticated,
    appleLogin
} from '../../redux-store/slices/customerSlice';
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';


// Extend the Window interface to include AppleID
declare global {
    interface Window {
        AppleID?: any;
        google?: any;
        profilePicUrl?: string; // Global variable to store profile pic URL
    }
}

interface SignupFormData {
    email: string;
    password: string;
    confirmPassword: string;
    referralCode?: string;
    firstName: string;
    lastName: string;
    phone: string;
    birthDate: string;
}

interface AppleSignInResponse {
    authorization: {
        code: string;
        id_token?: string;
    };
    user?: {
        name?: {
            firstName?: string;
            lastName?: string;
        };
        email?: string;
    };
}

interface GoogleCredentialResponse {
    credential: string;
    select_by?: string;
}

// Enhanced Yup validation schema
const validationSchema = Yup.object({
    firstName: Yup.string()
        .trim()
        .min(2, 'First name must be at least 2 characters')
        .max(50, 'First name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'First name can only contain letters, spaces, hyphens, and apostrophes')
        .required('First name is required'),
    lastName: Yup.string()
        .trim()
        .min(2, 'Last name must be at least 2 characters')
        .max(50, 'Last name must be less than 50 characters')
        .matches(/^[a-zA-Z\s'-]+$/, 'Last name can only contain letters, spaces, hyphens, and apostrophes')
        .required('Last name is required'),
    email: Yup.string()
        .trim()
        .email('Please enter a valid email address')
        .max(320, 'Email address is too long')
        .required('Email is required'),
    phone: Yup.string()
        .trim()
        .matches(/^\+?[\d\s\-\(\)]{10,20}$/, 'Please enter a valid phone number (10-20 digits)')
        .nullable()
        .transform((value) => value === '' ? null : value),
    birthDate: Yup.date()
        .max(new Date(), 'Birth date cannot be in the future')
        .test('age', 'You must be at least 13 years old', function(value) {
            if (!value) return true; // Allow empty birth date
            const today = new Date();
            const birthDate = new Date(value);
            const age = today.getFullYear() - birthDate.getFullYear();
            const monthDiff = today.getMonth() - birthDate.getMonth();
            
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                return age - 1 >= 13;
            }
            return age >= 13;
        })
        .nullable()
        .transform((value) => value === '' ? null : value),
    password: Yup.string()
        .min(8, 'Password must be at least 8 characters long')
        .max(128, 'Password must be less than 128 characters')
        .matches(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&#+\-_=])[A-Za-z\d@$!%*?&#+\-_=]*$/,
            'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        )
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password')], 'Passwords do not match')
        .required('Please confirm your password'),
    referralCode: Yup.string()
        .trim()
        .max(50, 'Referral code is too long')
        .nullable()
        .transform((value) => value === '' ? null : value)
});

const SignupPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const dispatch = useDispatch<AppDispatch>();

    // Redux selectors
    const isLoading = useSelector(selectCustomerLoading);
    const error = useSelector(selectCustomerError);
    const isAuthenticated = useSelector(selectIsAuthenticated);
    
    // Local state
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [socialLoading, setSocialLoading] = useState<{
        google: boolean;
        apple: boolean;
    }>({ google: false, apple: false });
    const [socialState, setSocialState] = useState<any>({
        google: { initialized: true },
        apple: { initialized: true }
    });

    // Image upload state
    const [_selectedImage, setSelectedImage] = useState<File | null>(null);
    const [previewUrl, setPreviewUrl] = useState<string>('');
    const [uploadingImage, setUploadingImage] = useState(false);
    const [uploadedImageUrl, setUploadedImageUrl] = useState<string>('');
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Load profile picture URL from localStorage on component mount
    useEffect(() => {
        const savedProfilePicUrl = localStorage.getItem('tempProfilePicUrl');
        if (savedProfilePicUrl) {
            setUploadedImageUrl(savedProfilePicUrl);
            setPreviewUrl(savedProfilePicUrl);
            console.log('Loaded profile pic URL from localStorage:', savedProfilePicUrl);
        }
    }, []);

    // Clear profile picture function
    const clearProfilePicture = useCallback(() => {
        console.log('Clear profile picture button clicked');

        // Clear all state
        setSelectedImage(null);
        setPreviewUrl('');
        setUploadedImageUrl('');

        // Clear global variables
        window.profilePicUrl = undefined;

        // Clear localStorage
        localStorage.removeItem('tempProfilePicUrl');

        // Reset file input using ref
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
            console.log('File input reset using ref');
        } else {
            // Fallback to querySelector
            const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
            if (fileInput) {
                fileInput.value = '';
                console.log('File input reset using querySelector');
            }
        }

        
        console.log('Profile picture cleared successfully');
    }, []);

    // Google authentication uses native popup, no custom modal needed

    // Initial form values
    const initialValues: SignupFormData = {
        email: '',
        password: '',
        confirmPassword: '',
        referralCode: '',
        firstName: '',
        lastName: '',
        phone: '',
        birthDate: ''
    };

    // Configuration from environment variables
    const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "************-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";

    // Log helper for testing real Google accounts
    console.log('🔧 To test with real Google accounts, run in console: simulateGoogleAccounts()');
    const APPLE_CLIENT_ID = import.meta.env.VITE_APPLE_NAME || "patronpalLoginKey";
    const APPLE_KEY_ID = import.meta.env.VITE_APPLE_KEY_ID || "3NP7Y9SS6L";
    const REDIRECT_URI = `${window.location.origin}/auth/apple/callback`;

    // Helper function to decode JWT token safely
    const parseJwtToken = useCallback((token: string) => {
        try {
            const base64Url = token.split('.')[1];
            if (!base64Url) throw new Error('Invalid token format');
            
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            const jsonPayload = decodeURIComponent(
                atob(base64)
                    .split('')
                    .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
                    .join('')
            );
            
            const parsed = JSON.parse(jsonPayload);
            
            // Validate required fields
            if (!parsed.email || !parsed.sub) {
                throw new Error('Missing required user information');
            }
            
            return parsed;
        } catch (error) {
            console.error('Error parsing JWT token:', error);
            return null;
        }
    }, []);

    // Enhanced Google response handler
    const handleGoogleResponse = useCallback(async (response: GoogleCredentialResponse) => {
        try {
            setSocialLoading(prev => ({ ...prev, google: true }));
            console.log('Google response received');
            
            if (!response?.credential) {
                throw new Error('No credential received from Google');
            }

            const userInfo = parseJwtToken(response.credential);
            if (!userInfo) {
                throw new Error('Failed to parse Google user information');
            }

            console.log('Processing Google user registration...');

            const result = await dispatch(googleRegister({
                Email: userInfo.email,
                googleId: userInfo.sub,
                FirstName: userInfo.given_name || '',
                LastName: userInfo.family_name || '',
                profile_pic: userInfo.picture || ''
            }));

            if (googleRegister.fulfilled.match(result)) {
                // Show SweetAlert2 success message for Google signup
                Swal.fire({
                    title: 'Welcome to PatronPal!',
                    html: `
                        <div style="text-align: center;">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTJMMTEgMTRMMTUgMTAiIHN0cm9rZT0iIzEwYjk4MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iIzEwYjk4MSIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=" alt="Success" style="width: 64px; height: 64px; margin-bottom: 16px;">
                            <p style="font-size: 16px; color: #333; margin: 0;">Google sign-up successful!</p>
                            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Welcome to PatronPal! Redirecting to dashboard...</p>
                        </div>
                    `,
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    customClass: {
                        popup: 'swal2-popup-custom'
                    }
                }).then(() => {
                    // Handle redirect after Google signup
                    const urlParams = new URLSearchParams(location.search);
                    const redirectParam = urlParams.get('redirect');
                    const typeParam = urlParams.get('type');
                    const redirectTo = redirectParam || '/';

                    // Preserve order type parameter when redirecting to checkout
                    if (redirectTo === '/checkout') {
                        const checkoutUrl = typeParam ? `/checkout?type=${typeParam}` : '/checkout';
                        window.location.href = checkoutUrl;
                    } else {
                        navigate(redirectTo);
                    }
                });
            } else if (googleRegister.rejected.match(result)) {
                const errorMessage = result.payload as string;
                if (errorMessage?.toLowerCase().includes('already exists') ||
                    errorMessage?.toLowerCase().includes('already registered')) {
                    // Show SweetAlert2 info message for existing account
                    Swal.fire({
                        title: 'Account Already Exists',
                        html: `
                            <div style="text-align: center;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iIzM5OGVmNCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xMiA4VjEyIiBzdHJva2U9IiMzOThlZjQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMiAxNkgxMi4wMSIgc3Ryb2tlPSIjMzk4ZWY0IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="Info" style="width: 64px; height: 64px; margin-bottom: 16px;">
                                <p style="font-size: 16px; color: #333; margin: 0;">Account already exists with this Google account</p>
                                <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Redirecting to login page...</p>
                            </div>
                        `,
                        icon: 'info',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'swal2-popup-custom'
                        }
                    }).then(() => {
                        navigate('/login');
                    });
                } else {
                    // Show SweetAlert2 error message for other Google signup failures
                    Swal.fire({
                        title: 'Google Sign-up Failed',
                        html: `
                            <div style="text-align: center;">
                                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
                                <p style="font-size: 16px; color: #333; margin: 0;">${errorMessage || 'Google sign-up failed'}</p>
                                <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again</p>
                            </div>
                        `,
                        icon: 'error',
                        showConfirmButton: false,
                        timer: 3000,
                        timerProgressBar: true,
                        customClass: {
                            popup: 'swal2-popup-custom'
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Google Sign-up error:', error);

            let errorMessage = 'Google sign-up failed. Please try again.';

            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }

            // Show SweetAlert2 error for catch block
            Swal.fire({
                title: 'Google Sign-up Error',
                html: `
                    <div style="text-align: center;">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
                        <p style="font-size: 16px; color: #333; margin: 0;">${errorMessage}</p>
                        <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or contact support</p>
                    </div>
                `,
                icon: 'error',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                customClass: {
                    popup: 'swal2-popup-custom'
                }
            });
        } finally {
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [dispatch, navigate, parseJwtToken]);

    // Enhanced Apple response handler
    const handleAppleResponse = useCallback(async (response: AppleSignInResponse) => {
        try {
            setSocialLoading(prev => ({ ...prev, apple: true }));
            console.log('Apple response received');

            if (!response?.authorization?.code) {
                throw new Error('No authorization code received from Apple');
            }

            const userEmail = response.user?.email || '';
            const firstName = response.user?.name?.firstName || '';
            const lastName = response.user?.name?.lastName || '';

            const appleData = {
                Email: userEmail,
                appleId: response.authorization.code,
                customToken: response.authorization.id_token || response.authorization.code,
                keyId: APPLE_KEY_ID,
                firstName: firstName,
                lastName: lastName
            };

            console.log('Processing Apple user registration...');

            const result = await dispatch(appleLogin(appleData) as any);

            if (result.type.endsWith('/fulfilled')) {
                toast.success('Apple sign-up successful! Welcome to PatronPal!');
                setTimeout(() => navigate('/'), 100);
            } else if (result.type.endsWith('/rejected')) {
                const errorMessage = result.payload as string;
                if (errorMessage?.toLowerCase().includes('already exists') || 
                    errorMessage?.toLowerCase().includes('already registered')) {
                    toast.info('Account already exists. Redirecting to login page.');
                    setTimeout(() => navigate('/login'), 1500);
                } else {
                    toast.error(errorMessage || 'Apple sign-up failed. Please try again.');
                }
            }
        } catch (error) {
            console.error('Apple Sign-up error:', error);
            
            let errorMessage = 'Apple sign-up failed. Please try again.';
            
            if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error. Please check your connection and try again.';
            } else if (error instanceof Error) {
                errorMessage = error.message;
            }
            
            toast.error(errorMessage);
        } finally {
            setSocialLoading(prev => ({ ...prev, apple: false }));
        }
    }, [dispatch, navigate, APPLE_KEY_ID]);

    // Simple Google Sign-In initialization to match LoginPage
    useEffect(() => {
        const initializeGoogle = () => {
            try {
                if (window.google?.accounts?.id) {
                    window.google.accounts.id.initialize({
                        client_id: GOOGLE_CLIENT_ID,
                        callback: handleGoogleResponse,
                        auto_select: false,
                        cancel_on_tap_outside: true,
                        use_fedcm_for_prompt: false,
                        context: 'signup',
                        ux_mode: 'popup'
                    });

                    window.google.accounts.id.disableAutoSelect();
                    setSocialState((prev: any) => ({ ...prev, google: { initialized: true } }));
                    console.log('✅ Google Sign-In initialized successfully');
                }
            } catch (error) {
                console.error('❌ Error initializing Google Sign-In:', error);
            }
        };

        // Initialize if Google is already loaded
        if (GOOGLE_CLIENT_ID && window.google?.accounts?.id) {
            initializeGoogle();
        }
    }, [GOOGLE_CLIENT_ID, handleGoogleResponse]);

    // Enhanced Apple Sign-In initialization
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        const initializeAppleSignIn = () => {
            try {
                if (window.AppleID?.auth) {
                    window.AppleID.auth.init({
                        clientId: APPLE_CLIENT_ID,
                        scope: 'name email',
                        redirectURI: REDIRECT_URI,
                        usePopup: true,
                        state: 'signup'
                    });
                    setSocialState((prev: any) => ({ ...prev, apple: { initialized: true } }));
                    console.log('Apple Sign-In initialized successfully');
                    return true;
                }
                return false;
            } catch (error) {
                console.error('Error initializing Apple Sign-In:', error);
                return false;
            }
        };

        const loadAppleScript = () => {
            const existingScript = document.querySelector('script[src*="appleid.cdn-apple.com"]');
            if (existingScript) {
                existingScript.remove();
            }

            const script = document.createElement('script');
            script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
            script.async = true;
            script.defer = true;
            
            script.onload = () => {
                timeoutId = setTimeout(() => {
                    const success = initializeAppleSignIn();
                    if (!success) {
                        setTimeout(() => {
                            initializeAppleSignIn();
                        }, 2000);
                    }
                }, 1000);
            };
            
            script.onerror = () => {
                console.error('Failed to load Apple Sign-In script');
                setSocialState((prev: any) => ({ ...prev, apple: { initialized: false } }));
            };
            
            document.head.appendChild(script);
        };

        if (window.AppleID?.auth) {
            initializeAppleSignIn();
        } else {
            loadAppleScript();
        }

        return () => {
            if (timeoutId) clearTimeout(timeoutId);
            const script = document.querySelector('script[src*="appleid.cdn-apple.com"]');
            if (script && script.parentNode) {
                script.parentNode.removeChild(script);
            }
        };
    }, [APPLE_CLIENT_ID, REDIRECT_URI]);

    // Redirect if already authenticated
    useEffect(() => {
        if (isAuthenticated) {
            navigate('/');
        }
    }, [isAuthenticated, navigate]);

    // Clear error when component unmounts
    useEffect(() => {
        return () => {
            dispatch(clearError());
        };
    }, [dispatch]);

    // Toggle functions
    const togglePasswordVisibility = useCallback(() => {
        setShowPassword(prev => !prev);
    }, []);

    const toggleConfirmPasswordVisibility = useCallback(() => {
        setShowConfirmPassword(prev => !prev);
    }, []);

    // Image upload handler
    const handleImageChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            setSelectedImage(file);

            // Create preview URL
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewUrl(reader.result as string);
            };
            reader.readAsDataURL(file);

            // Upload image immediately
            uploadImage(file);
        }
    }, []);

    // Upload image to server
    const uploadImage = useCallback(async (file: File) => {
        setUploadingImage(true);

        try {
            const formData = new FormData();
            formData.append('images', file);

            // Get the base URL from environment
            const baseURL = import.meta.env.VITE_LOCAL_API_URL || 'http://localhost:4444/api/v1';
            console.log('Uploading image to:', `${baseURL}/picture_upload`);

            const response = await fetch(`${baseURL}/picture_upload`, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();
            console.log('Image upload response:', data);

            if (data.myFile) {
                // Store URL in React state
                setUploadedImageUrl(data.myFile);

                // Also store in global variable as backup
                window.profilePicUrl = data.myFile;

                // Store in localStorage as another backup
                localStorage.setItem('tempProfilePicUrl', data.myFile);

                console.log('Profile picture URL set to:', data.myFile);
            } else {
                // toast.error('Failed to upload profile picture');
                console.error('Upload failed - no myFile in response:', data);
            }
        } catch (error) {
            console.error('Error uploading image:', error);
            toast.error('Error uploading image. Please try again.');
        } finally {
            setUploadingImage(false);
        }
    }, []);

    // Enhanced form submission
    const handleSubmit = useCallback(async (values: SignupFormData, { setSubmitting }: any) => {
        dispatch(clearError());

        try {
            // Trim and clean data
            const cleanedData: any = {
                Email: values.email.trim().toLowerCase(),
                Password: values.password,
                ConfirmPassword: values.confirmPassword,
                FirstName: values.firstName.trim(),
                LastName: values.lastName.trim(),
                Phone: values.phone.trim() || undefined,
                birthDate: values.birthDate || undefined,
                referralCode: values.referralCode?.trim() || undefined
            };

            // Get profile picture URL from multiple sources
            const profilePicUrl = uploadedImageUrl ||
                                window.profilePicUrl ||
                                localStorage.getItem('tempProfilePicUrl') ||
                                '';

            // Only add profile_pic if we have an uploaded image URL
            if (profilePicUrl && profilePicUrl.trim()) {
                cleanedData.profile_pic = profilePicUrl.trim();
            }

            console.log('Submitting registration with data:', cleanedData);
            console.log('Profile picture URL being sent:', profilePicUrl);
            console.log('uploadedImageUrl state value:', uploadedImageUrl);
            console.log('window.profilePicUrl value:', window.profilePicUrl);
            console.log('localStorage tempProfilePicUrl:', localStorage.getItem('tempProfilePicUrl'));

            const result = await dispatch(registerCustomer(cleanedData));

            if (registerCustomer.fulfilled.match(result)) {
                // Clear profile picture state and storage
                setSelectedImage(null);
                setPreviewUrl('');
                setUploadedImageUrl('');
                window.profilePicUrl = undefined;
                localStorage.removeItem('tempProfilePicUrl');

                // Show SweetAlert2 with email verification message
                Swal.fire({
                    title: 'Please Verify Your Email',
                    html: `
                        <div style="text-align: center;">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTQgNEgyMEMyMS4xIDQgMjIgNC45IDIyIDZWMThDMjIgMTkuMSAyMS4xIDIwIDIwIDIwSDRDMi45IDIwIDIgMTkuMSAyIDE4VjZDMiA0LjkgMi45IDQgNCA0WiIgc3Ryb2tlPSIjNDI4NUY0IiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiLz4KPHBhdGggZD0iTTIyIDZMMTIgMTNMMiA2IiBzdHJva2U9IiM0Mjg1RjQiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPgo8L3N2Zz4K" alt="Email" style="width: 64px; height: 64px; margin-bottom: 16px;">
                            <p style="font-size: 16px; color: #333; margin: 0;">OTP has been sent to your email</p>
                            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">${cleanedData.Email.toLowerCase()}</p>
                        </div>
                    `,
                    icon: 'success',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    customClass: {
                        popup: 'swal2-popup-custom'
                    }
                }).then(() => {
                    // Get redirect parameters from URL
                    const urlParams = new URLSearchParams(location.search);
                    const redirectParam = urlParams.get('redirect');
                    const typeParam = urlParams.get('type');

                    navigate('/verify-otp', {
                        state: {
                            email: cleanedData.Email.toLowerCase(),
                            redirectTo: redirectParam,
                            orderType: typeParam
                        }
                    });
                });
            } else if (registerCustomer.rejected.match(result)) {
                const errorMessage = result.payload as string;
                toast.error(errorMessage || 'Registration failed. Please try again.');
            }
        } catch (error) {
            console.error('Registration error:', error);
            toast.error('An unexpected error occurred. Please try again.');
        } finally {
            setSubmitting(false);
        }
    }, [dispatch, navigate]);

    // Google Sign-In handler - direct to Google's native popup
    const handleGoogleSignIn = useCallback(async () => {
        if (socialLoading.google) {
            return; // Prevent multiple clicks
        }

        // Set loading state immediately
        setSocialLoading(prev => ({ ...prev, google: true }));

        try {
            console.log('🔄 Starting Google Sign-In...');

            if (window.google?.accounts?.id) {
                // Initialize Google with popup mode for direct account selection
                window.google.accounts.id.initialize({
                    client_id: GOOGLE_CLIENT_ID,
                    callback: handleGoogleResponse,
                    auto_select: false,
                    cancel_on_tap_outside: false,
                    ux_mode: 'popup'
                });

                // Create invisible button and trigger it immediately
                const tempContainer = document.createElement('div');
                tempContainer.style.position = 'absolute';
                tempContainer.style.left = '-9999px';
                tempContainer.style.top = '-9999px';
                document.body.appendChild(tempContainer);

                // Render Google button
                window.google.accounts.id.renderButton(tempContainer, {
                    theme: 'outline',
                    size: 'large',
                    type: 'standard',
                    shape: 'rectangular',
                    text: 'signin_with',
                    logo_alignment: 'left',
                    width: 300
                });

                // Immediately trigger the button click to open Google's popup
                setTimeout(() => {
                    const googleButton = tempContainer.querySelector('div[role="button"]') as HTMLElement;
                    if (googleButton) {
                        googleButton.click();
                        console.log('✅ Triggered Google popup');
                        setSocialLoading(prev => ({ ...prev, google: false }));
                    } else {
                        console.log('❌ Could not find Google button, showing fallback');
                        tryAlternativeGoogleSignIn();
                    }

                    // Clean up the temporary container
                    setTimeout(() => {
                        if (document.body.contains(tempContainer)) {
                            document.body.removeChild(tempContainer);
                        }
                    }, 1000);
                }, 100);

            } else {
                console.log('Google API not available, trying to load...');
                // Try to load Google API if not available
                await loadGoogleAPI();
                // Retry after loading
                setTimeout(() => handleGoogleSignIn(), 500);
            }

        } catch (error) {
            console.error('❌ Error starting Google Sign-In:', error);
            Swal.fire({
                title: 'Failed to Start Google Sign-In',
                html: `
                    <div style="text-align: center;">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
                        <p style="font-size: 16px; color: #333; margin: 0;">Failed to start Google Sign-In</p>
                        <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or refresh the page</p>
                    </div>
                `,
                icon: 'error',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                customClass: {
                    popup: 'swal2-popup-custom'
                }
            });
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [socialLoading.google, GOOGLE_CLIENT_ID, handleGoogleResponse]);

    // Load Google API if not available
    const loadGoogleAPI = useCallback(async () => {
        return new Promise<void>((resolve, reject) => {
            if (window.google?.accounts?.id) {
                resolve();
                return;
            }

            // Check if script already exists
            const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
            if (existingScript) {
                // Wait for it to load
                let attempts = 0;
                const checkLoaded = () => {
                    if (window.google?.accounts?.id || attempts > 50) {
                        resolve();
                    } else {
                        attempts++;
                        setTimeout(checkLoaded, 100);
                    }
                };
                checkLoaded();
                return;
            }

            // Create and load the script
            const script = document.createElement('script');
            script.src = 'https://accounts.google.com/gsi/client';
            script.async = true;
            script.defer = true;

            script.onload = () => {
                console.log('✅ Google API loaded successfully');
                resolve();
            };

            script.onerror = () => {
                console.error('❌ Failed to load Google API');
                reject(new Error('Failed to load Google API'));
            };

            document.head.appendChild(script);
        });
    }, []);

    // Alternative Google Sign-In method using renderButton
    const tryAlternativeGoogleSignIn = useCallback(() => {
        try {
            console.log('🔄 Trying alternative Google Sign-In method...');

            if (!window.google?.accounts?.id) {
                console.error('Google API still not available');
                Swal.fire({
                    title: 'Google Sign-In Unavailable',
                    html: `
                        <div style="text-align: center;">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2Y5NzMxNiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xMiA4VjEyIiBzdHJva2U9IiNmOTczMTYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMiAxNkgxMi4wMSIgc3Ryb2tlPSIjZjk3MzE2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="Warning" style="width: 64px; height: 64px; margin-bottom: 16px;">
                            <p style="font-size: 16px; color: #333; margin: 0;">Google Sign-In is not available</p>
                            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please refresh the page and try again</p>
                        </div>
                    `,
                    icon: 'warning',
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    customClass: {
                        popup: 'swal2-popup-custom'
                    }
                });
                setSocialLoading(prev => ({ ...prev, google: false }));
                return;
            }

            // Re-initialize to ensure fresh state
            window.google.accounts.id.initialize({
                client_id: GOOGLE_CLIENT_ID,
                callback: handleGoogleResponse,
                auto_select: false,
                cancel_on_tap_outside: false,
                ux_mode: 'popup'
            });

            // Create a visible container for Google button (temporarily)
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'fixed';
            tempContainer.style.top = '50%';
            tempContainer.style.left = '50%';
            tempContainer.style.transform = 'translate(-50%, -50%)';
            tempContainer.style.zIndex = '10000';
            tempContainer.style.backgroundColor = 'white';
            tempContainer.style.padding = '20px';
            tempContainer.style.borderRadius = '8px';
            tempContainer.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
            document.body.appendChild(tempContainer);

            // Add a message
            const message = document.createElement('div');
            message.textContent = 'Click the Google button below to continue:';
            message.style.marginBottom = '10px';
            message.style.textAlign = 'center';
            tempContainer.appendChild(message);

            // Render Google button
            const buttonContainer = document.createElement('div');
            tempContainer.appendChild(buttonContainer);

            window.google.accounts.id.renderButton(buttonContainer, {
                theme: 'outline',
                size: 'large',
                type: 'standard',
                shape: 'rectangular',
                text: 'signin_with',
                logo_alignment: 'left',
                width: 300
            });

            // Add close button
            const closeButton = document.createElement('button');
            closeButton.textContent = 'Cancel';
            closeButton.style.marginTop = '10px';
            closeButton.style.width = '100%';
            closeButton.style.padding = '8px';
            closeButton.style.border = '1px solid #ccc';
            closeButton.style.borderRadius = '4px';
            closeButton.style.backgroundColor = '#f5f5f5';
            closeButton.style.cursor = 'pointer';
            closeButton.onclick = () => {
                if (document.body.contains(tempContainer)) {
                    document.body.removeChild(tempContainer);
                }
                setSocialLoading(prev => ({ ...prev, google: false }));
            };
            tempContainer.appendChild(closeButton);

            // Auto-remove after 30 seconds
            setTimeout(() => {
                if (document.body.contains(tempContainer)) {
                    document.body.removeChild(tempContainer);
                    setSocialLoading(prev => ({ ...prev, google: false }));
                }
            }, 30000);

        } catch (error) {
            console.error('❌ Alternative Google Sign-In failed:', error);
            Swal.fire({
                title: 'Google Sign-In Failed',
                html: `
                    <div style="text-align: center;">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
                        <p style="font-size: 16px; color: #333; margin: 0;">Failed to show Google Sign-In</p>
                        <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or contact support</p>
                    </div>
                `,
                icon: 'error',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                customClass: {
                    popup: 'swal2-popup-custom'
                }
            });
            setSocialLoading(prev => ({ ...prev, google: false }));
        }
    }, [GOOGLE_CLIENT_ID, handleGoogleResponse]);

    // Google authentication now uses native popup - no custom functions needed

    // Enhanced Apple Sign-In handler
    const handleAppleSignIn = useCallback(async () => {
        if (socialLoading.apple || isLoading) {
            toast.warning('Please wait for the current operation to complete.');
            return;
        }

        if (!socialState.apple.initialized) {
            toast.error('Apple Sign-In is still loading. Please wait and try again.');
            return;
        }

        if (!window.AppleID?.auth) {
            toast.error('Apple Sign-In is not available. Please refresh the page.');
            return;
        }

        try {
            const response = await window.AppleID.auth.signIn({
                usePopup: true
            });
            
            await handleAppleResponse(response);
        } catch (error: any) {
            console.error('Apple Sign-In error:', error);
            
            // Don't show error for user cancelled action
            if (error === 'popup_closed_by_user' || error?.error === 'popup_closed_by_user') {
                return;
            }
            
            // Handle specific Apple Sign-In errors
            const errorMessages: { [key: string]: string } = {
                'invalid_client': 'Apple Sign-In configuration error. Please contact support.',
                'invalid_request': 'Invalid Apple Sign-In request. Please try again.',
                'server_error': 'Apple Sign-In server error. Please try again later.',
                'access_denied': 'Apple Sign-In was cancelled.'
            };
            
            const errorMessage = error?.error ? 
                errorMessages[error.error] || 'Failed to sign in with Apple. Please try again.' :
                'Failed to sign in with Apple. Please try again.';
                
            toast.error(errorMessage);
        }
    }, [socialLoading.apple, isLoading, socialState.apple.initialized, handleAppleResponse]);

    // Helper to disable form fields/buttons during loading or submitting
    const isFormDisabled = isLoading || socialLoading.google || socialLoading.apple;

    return (
        <div className="flex justify-center items-center min-h-screen bg-gray-50 p-5">
            <div className="w-full max-w-md">
                <div className="text-left mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">
                        Patron<span className='text-[#FF5C00]'>Pal</span>
                    </h1>
                    <h2 className="text-xl font-semibold mt-4 mb-2">Create your Account</h2>
                    <p className="text-gray-600 text-sm">
                        Join PatronPal to manage your customer experience.
                    </p>
                </div>

                {/* Display error message if there's an error */}
                {error && (
                    <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                        {error}
                    </div>
                )}

                <Formik
                    initialValues={initialValues}
                    validationSchema={validationSchema}
                    onSubmit={handleSubmit}
                    validateOnBlur={true}
                    validateOnChange={false}
                >
                    {({ values, isValid, isSubmitting, errors, touched }) => (
                        <Form className="space-y-4" noValidate>
                            {/* Profile Picture Upload */}
                            <div className="mb-6">
                                <div className="flex flex-col items-center">
                                    <div className="relative w-full h-32 mb-4 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex justify-center items-center overflow-hidden">
                                        {previewUrl ? (
                                            <>
                                                <img
                                                    src={previewUrl}
                                                    alt="Profile preview"
                                                    className="w-[130px] h-full rounded-full"
                                                />
                                                <button
                                                    type="button"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        console.log('X button clicked');
                                                        clearProfilePicture();
                                                    }}
                                                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors z-20"
                                                    title="Remove image"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                    </svg>
                                                </button>
                                            </>
                                        ) : (
                                            <div className="text-center p-4">
                                                <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                                <p className="mt-1 text-sm text-gray-500">
                                                    Click to upload a profile picture
                                                </p>
                                                <p className="text-xs text-gray-400">
                                                    PNG, JPG, GIF up to 10MB
                                                </p>
                                            </div>
                                        )}
                                        <input
                                            ref={fileInputRef}
                                            type="file"
                                            accept="image/*"
                                            className={`absolute inset-0 w-full h-full opacity-0 ${!previewUrl && !uploadingImage ? 'cursor-pointer' : 'cursor-not-allowed'}`}
                                            onChange={handleImageChange}
                                            disabled={uploadingImage || !!previewUrl}
                                        />
                                        {uploadingImage && (
                                            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
                                            </div>
                                        )}
                                    </div>
                                    {/* {uploadedImageUrl && (
                                        <p className="text-xs text-green-600 mb-2">
                                            ✓ Profile picture ready for account creation
                                        </p>
                                    )} */}
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                                        First Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="firstName"
                                        name="firstName"
                                        autoComplete="given-name"
                                        placeholder="John"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.firstName && touched.firstName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="firstName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                                <div>
                                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                                        Last Name *
                                    </label>
                                    <Field
                                        type="text"
                                        id="lastName"
                                        name="lastName"
                                        autoComplete="family-name"
                                        placeholder="Doe"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                            errors.lastName && touched.lastName ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <ErrorMessage name="lastName" component="div" className="text-red-600 text-xs mt-1" />
                                </div>
                            </div>

                            <div>
                                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                    Email *
                                </label>
                                <Field
                                    type="email"
                                    id="email"
                                    name="email"
                                    autoComplete="email"
                                    placeholder="<EMAIL>"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.email && touched.email ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="email" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                                    Phone Number
                                </label>
                                <Field
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    autoComplete="tel"
                                    placeholder="(*************"
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.phone && touched.phone ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="phone" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="birthDate" className="block text-sm font-medium text-gray-700 mb-1">
                                    Date of Birth
                                </label>
                                <Field
                                    type="date"
                                    id="birthDate"
                                    name="birthDate"
                                    max={new Date().toISOString().split('T')[0]}
                                    className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors ${
                                        errors.birthDate && touched.birthDate ? 'border-red-500' : 'border-gray-300'
                                    }`}
                                    disabled={isLoading || isSubmitting}
                                />
                                <ErrorMessage name="birthDate" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <div>
                                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                    Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showPassword ? 'text' : 'password'}
                                        id="password"
                                        name="password"
                                        autoComplete="new-password"
                                        placeholder="Create a strong password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.password && touched.password ? 'border-red-500' : 'border-gray-300'
                                        }`}
                                        disabled={isLoading || isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        onClick={togglePasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500 hover:text-gray-700"
                                        disabled={isLoading || isSubmitting}
                                    >
                                        {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="password" component="div" className="text-red-600 text-xs mt-1" />
                                <p className="mt-1 text-xs text-gray-500">
                                    Must contain uppercase, lowercase, number, and special character
                                </p>
                            </div>

                            <div>
                                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                                    Confirm Password *
                                </label>
                                <div className="relative">
                                    <Field
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        id="confirmPassword"
                                        name="confirmPassword"
                                        autoComplete="new-password"
                                        placeholder="Confirm your password"
                                        className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] pr-10 transition-colors ${
                                            errors.confirmPassword && touched.confirmPassword 
                                                ? 'border-red-300 focus:ring-red-500' 
                                                : 'border-gray-300'
                                        }`}
                                        disabled={isFormDisabled}
                                    />
                                    <button
                                        type="button"
                                        onClick={toggleConfirmPasswordVisibility}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                                        disabled={isFormDisabled}
                                    >
                                        {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                                    </button>
                                </div>
                                <ErrorMessage name="confirmPassword" component="div" className="text-red-600 text-xs mt-1" />
                                {values.password && values.confirmPassword && values.password !== values.confirmPassword && (
                                    <p className="mt-1 text-sm text-red-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                        </svg>
                                        Passwords do not match
                                    </p>
                                )}
                                {values.password && values.confirmPassword && values.password === values.confirmPassword && (
                                    <p className="mt-1 text-sm text-green-600 flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                        </svg>
                                        Passwords match
                                    </p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="referralCode" className="block text-sm font-medium text-gray-700 mb-1">
                                    Referral Code <span className="text-gray-400">(Optional)</span>
                                </label>
                                <Field
                                    type="text"
                                    id="referralCode"
                                    name="referralCode"
                                    placeholder="Enter referral code if you have one"
                                    className="w-full px-3 py-2 bg-white border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#FF5C00] transition-colors"
                                    disabled={isFormDisabled}
                                />
                                <ErrorMessage name="referralCode" component="div" className="text-red-600 text-xs mt-1" />
                            </div>

                            <button
                                type="submit"
                                disabled={isSubmitting || !isValid}
                                className="w-full bg-[#FF5C00] text-white py-2.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium hover:bg-[#e54f00] hover:shadow-md"
                            >
                                {isLoading ? (
                                    <span className="flex items-center justify-center">
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Creating Account...
                                    </span>
                                ) : (
                                    'Create Account'
                                )}
                            </button>
                        </Form>
                    )}
                </Formik>

                <div className="mt-6 text-center">
                    <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                            <div className="w-full border-t border-gray-300" />
                        </div>
                        <div className="relative flex justify-center text-sm">
                            <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
                        </div>
                    </div>
                </div>

                <div className="mt-4 space-y-3">
                    <button
                        id="google-signin-button"
                        onClick={handleGoogleSignIn}
                        disabled={isLoading || socialLoading.google || !socialState.google || isFormDisabled}
                        className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
                        type="button"
                    >
                        {socialLoading.google ? (
                            <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <FcGoogle className="w-5 h-5 mr-2" />
                        )}
                        {!socialState.google ? 'Loading Google...' : 'Continue with Google'}
                    </button>

                    <button
                        onClick={handleAppleSignIn}
                        disabled={isFormDisabled || !socialState.apple.initialized}
                        className="flex items-center justify-center w-full px-4 py-2.5 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:shadow-sm cursor-pointer"
                    >
                        {socialLoading.apple ? (
                            <svg className="animate-spin w-5 h-5 mr-2 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        ) : (
                            <FaApple className="w-5 h-5 mr-2" />
                        )}
                        {socialLoading.apple ? 'Signing up...' : !socialState.apple.initialized ? 'Loading Apple...' : 'Continue with Apple'}
                    </button>
                </div>

                <div className="mt-6 text-center">
                    <p className="text-sm text-gray-600">
                        Already have an account?{' '}
                        <Link to="/login" className="text-[#FF5C00] hover:text-[#e54f00] font-medium transition-colors">
                            Sign in here
                        </Link>
                    </p>
                </div>

                {/* Terms and Privacy */}
                <div className="mt-4 text-center">
                    <p className="text-xs text-gray-500">
                        By creating an account, you agree to our{' '}
                        <Link to="/terms" className="text-[#FF5C00] hover:underline">Terms of Service</Link>
                        {' '}and{' '}
                        <Link to="/privacy" className="text-[#FF5C00] hover:underline">Privacy Policy</Link>
                    </p>
                </div>
            </div>

            {/* Google's native account picker will be shown automatically */}
        </div>
    );
};

export default SignupPage;