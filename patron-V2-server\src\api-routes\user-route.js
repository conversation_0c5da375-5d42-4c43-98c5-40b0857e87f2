import express  from "express";
const routes=express.Router();

import {getUser,
    getSuperUser,
    login,
    deleteUser,
    updateUser,
    getUserById,
    updateSuperUser,
    loginkiosk,
    postUser,
    getUserByStripeId
} from "../api/user.js"

routes.get('/User', getUser )
routes.get('/superadmin', getSuperUser )
routes.get('/user/:_id',getUserById)
routes.get('/user',getUserByStripeId)
routes.post('/login', login )
routes.post('/user', postUser )
routes.post('/loginKiosk', loginkiosk )
routes.put('/user/:_id',updateUser)
routes.put('/superUser/:_id',updateSuperUser)
routes.delete('/user/:_id',  deleteUser)



export default routes