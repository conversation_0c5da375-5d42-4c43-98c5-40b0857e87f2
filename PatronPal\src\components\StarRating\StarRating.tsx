import React from 'react';
import { Star } from 'lucide-react';

interface StarRatingProps {
  rating: number;
  size?: number;
  className?: string;
  showRating?: boolean;
}

const StarRating: React.FC<StarRatingProps> = ({ 
  rating, 
  size = 16, 
  className = "",
  showRating = false 
}) => {
  const validRating = isNaN(rating) ? 0 : Math.max(0, Math.min(5, rating));

  return (
    <div className={`flex items-center ${className}`}>
      {[1, 2, 3, 4, 5].map((star) => {
        const fillPercentage = Math.max(0, Math.min(100, (validRating - star + 1) * 100));
        
        return (
          <div key={star} className="relative mr-0.5">
            {/* Background star (empty) */}
            <Star
              size={size}
              color="#FF6B00"
              fill="none"
              className="absolute"
            />
            {/* Foreground star (filled based on percentage) */}
            <div 
              className="overflow-hidden"
              style={{ width: `${fillPercentage}%` }}
            >
              <Star
                size={size}
                color="#FF6B00"
                fill="#FF6B00"
              />
            </div>
          </div>
        );
      })}
      {showRating && (
        <span className="ml-1 text-sm font-medium">
          {validRating > 0 ? validRating.toFixed(1) : 'New'}
        </span>
      )}
    </div>
  );
};

export default StarRating;
