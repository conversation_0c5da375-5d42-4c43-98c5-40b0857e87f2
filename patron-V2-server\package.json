{"name": "new-pateron-angular-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js --ignore projects/", "lint": "eslint --fix src", "test": "echo \"Test OK\" && exit 0"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@doordash/sdk": "^0.6.12", "aws-sdk": "^2.1277.0", "axios": "^1.10.0", "bcrypt": "^5.0.1", "body-parser": "^1.20.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.2", "fs": "^0.0.1-security", "googleapis": "^107.0.0", "helmet": "^5.0.2", "joi": "^17.6.0", "jsonwebtoken": "^8.5.1", "moment": "^2.30.1", "mongoose": "^6.3.1", "morgan": "^1.10.0", "multer": "^1.4.4", "multer-s3": "^2.10.0", "node-cache": "^5.1.2", "node-onvif": "^0.1.7", "nodemailer": "^6.7.5", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "pdf-to-printer": "^5.6.0", "puppeteer": "^21.0.3", "simple-git": "^3.28.0", "socket.io": "^4.6.2", "stripe": "^10.3.0", "twilio": "^3.83.3", "uuid": "^9.0.0"}, "engines": {"node": "v16.15.0", "npm": "8.5.5"}, "devDependencies": {"@types/redis": "^4.0.11", "eslint": "^7.14.0", "eslint-config-airbnb-base": "^14.2.1", "eslint-plugin-import": "^2.22.1", "mocha": "^8.2.1", "nodemon": "^2.0.15", "supertest": "^6.0.1"}}