import { User, Store, Truck } from 'lucide-react';
import { assets } from '../../assets/assets';
import { Link } from 'react-router-dom';

const LiveTrackingPage = () => {
    // Delivery status information
    const deliveryInfo = {
        estimatedTime: "16:00 - 16:00",
        status: "The rider has picked your food.\nThey are heading towards you."
    };

    return (
        <div className="bg-gray-50 min-h-screen">
            <div className=" px-2 mx-auto min-h-screen font-sans">
                {/* Estimated Delivery Time */}
                <div className="text-center py-8">
                    <p className="text-gray-400 text-sm mb-1">Estimated Time of Delivery</p>
                    <p className="text-2xl font-semibold">{deliveryInfo.estimatedTime}</p>
                </div>

                {/* Map Container */}
                <div className="relative md:h-[394px] h-[234px] w-full bg-gray-100 overflow-hidden">
                    {/* Map Image (placeholder) */}
                    <div className="absolute inset-0 bg-[#f2f7f2]">
                        {/* Simulated map with streets */}
                        <img className='h-full w-full bg-cover' src={assets.mapImage} alt="" />
                        <div className="absolute inset-0">
                        </div>

                        {/* Restaurant Marker */}
                        <div className="absolute left-[38%] top-[14%]">
                            <div className="flex items-center justify-center w-10 h-10 bg-white rounded-full shadow-md">
                                <Store size={20} />
                            </div>
                        </div>

                        {/* Rider Marker */}
                        <div className="absolute left-[50%] top-[26%]">
                            <div className="flex items-center justify-center w-12 h-12 bg-primary rounded-full shadow-md">
                                <Truck size={24} color="white" />
                            </div>
                        </div>

                        {/* User Marker */}
                        <div className="absolute right-[20%] top-[65%]">
                            <div className="flex items-center justify-center w-10 h-10 bg-white rounded-full shadow-md">
                                <User size={20} />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Status Message */}
                <div className="text-center py-8">
                    {deliveryInfo.status.split('\n').map((line, index) => (
                        <p key={index} className="text-lg font-medium">
                            {line}
                        </p>
                    ))}
                </div>

                {/* Back to Home Button */}
                <div className="flex justify-center pb-10">
                    <Link to={'/'} className="bg-primary text-white rounded-full py-3 px-8 font-medium">
                        Back to Home
                    </Link>
                </div>
            </div>
        </div>
    );
};

export default LiveTrackingPage;