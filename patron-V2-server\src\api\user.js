import { superUser, User } from '../models/User.js'
import mongoose from 'mongoose'
import { employee, employeeType } from '../models/employee.js'
import jwt from "jsonwebtoken";
import productModel from '../models/product.js'
import categoryModel from '../models/category.js'
import parentCatModel from '../models/parentcategory.js'
import customerModel from '../models/customer.js'
import tablesModel from '../models/tables.js'
import taxModel from '../models/tax.js'
import supplierModel from '../models/supplier.js'
import siteModel from '../models/sitemanagement.js'
import stockModel from '../models/stock-wastage.js'
import { Modifier } from '../models/productmodifier.js'
import recieptModel from '../models/reciept.js'
import paymentsModel from '../models/paymentList.js'
// import {employeeModel,employeeType} from '../models/employee.js';

import orderModel from '../models/order.js'
import orderItemsModel from '../models/orderitem.js'
import loyalityModel from '../models/loyalty-offers.js'
import ingredientModel from '../models/ingredients.js'
import ingredientCatModel from '../models/ingredient-category.js'
import employeetimeModel from '../models/employeetime.js'
import deviceModel from '../models/device.js'
import coupenModel from '../models/coupens.js'
import blogModel from '../models/blog.js'
import wastageModel from '../models/stock-wastage.js';
// import productModel from '../api/product.js'

export const getUser = async (req, res) => {
  const user = await User.find(req)
  res.send(user)
}
export const getUserById = async (req, res) => {
  const user = await User.find(req.params)
  res.send(user)
}
export const getUserByStripeId = async (req, res) => {
  let filter = {}
  if (req.query.
    stripe_account_id
  ) {
    filter = {
      stripe_account_id
        : req.query.
          stripe_account_id
          .split(',')
    }
  }
  const user = await User.find(filter)
  res.send(user)
}
export const getSuperUser = async (req, res) => {
  const user = await superUser.find(req)
  res.send(user)
}

export const login = async (req, res) => {
  const { email, password } = req.body;
  console.log('email: ', email);
  const user = await User.findOne({ email: email }) || await superUser.findOne({ email: email })
  console.log('user: ', user);

  // return
  if (user?.email != email) {
    return res.status(400).send({ message: "User not found" });
  }
  if (user.password !== password) {
    return res.status(400).send({ message: "Wrong password" });
  }
  console.log('role: ', user.role);
  if (user.role == 'admin' || user.role == 'superadmin') {
    const token = jwt.sign({ _id: user._id }, process.env.JWT_SECRET);
    const userId = { _id: user._id }
    const role = user.role;
    const name = user;
    const appFee = user.appFee;
    const loginDate = user.createdDate;

    console.log('name:432423 ', name);
    return res.send({ message: "user login successfully", token, userId, role, loginDate, email: user.email, appFee, name });
  }

}
export const postUser = async (req, res) => {
  const { userId, name, email, password, role, appFee, surChargeThreshold, isActive } = req.body
  await new User({ userId, name, email, password, role, appFee, surChargeThreshold, isActive }).save().then(result => {
    res.status(200).json({
      name: result.name,
      email: result.email,
      password: result.password,
      role: result.role,
      userId: result.userId,
      appFee: result.appFee,
      surChargeThreshold: result.surChargeThreshold,
      isActive: result.isActive
    })
  }).catch(err => {
    console.log('err: ', err);

  })
}
export const loginkiosk = async (req, res) => {
  const { email, password } = req.body;
  console.log('email: ', email);
  const user = await User.findOne({ email: email }) || await superUser.findOne({ email: email })
  console.log('user: ', user);
  // return
  if (user?.email !== email) {
    return res.status(400).send({ message: "User not found" });
  }
  if (user.password !== password) {
    return res.status(400).send({ message: "Wrong password" });
  }
  console.log('role: ', user.role);
  if (user.role == 'admin' || user.role == 'superadmin') {
    const token = jwt.sign({ _id: user._id }, process.env.JWT_SECRET);
    const userId = { _id: user._id }
    const role = user.role;
    const appFee = user.appFee;
    const loginDate = user.createdDate
    return res.send({ message: "user login successfully", token, userId, role, loginDate, email: user.email, appFee });
  }

}

export const updateUser = async (req, res) => {
  console.log(req.params)
  let data = await User.findByIdAndUpdate(
    { _id: req.params._id }, {
    $set: req.body
  }, { new: true }
  );
  if (data) {
    res.send({ message: "User data updated successfully" });
  }
  else {
    res.send({ message: "User data cannot be updated successfully" })
  }
}
export const updateSuperUser = async (req, res) => {
  console.log(req.params)
  let data = await superUser.findByIdAndUpdate(
    { _id: req.params._id }, {
    $set: req.body
  }, { new: true }
  );
  if (data) {
    res.send({ message: "superUser data updated successfully" });
  }
  else {
    res.send({ message: "superUser data cannot be updated successfully" })
  }
}
export const deleteUser = async (req, res) => {
  console.log(req.params)
  const { _id } = req.params
  deleteUserData(_id).then(res => {
    res.send({ message: "User and related data delete successfully" });
  }).catch(err => {
    res.send({ message: "User data cannot delete successfully" })
  })
}


async function deleteUserData(userId) {
  const modelNamesAndSchemas = [
    { modelName: 'product', model: productModel },
    { modelName: 'category', model: categoryModel },
    // { modelName: 'emplyeeTime', model: employeetimeModel },
    { modelName: 'Employee', model: employee },
    { modelName: 'EmployeeType', model: employeeType },
    { modelName: 'parentcategory', model: parentCatModel },
    { modelName: 'tax', model: taxModel },
    { modelName: 'tables', model: tablesModel },
    { modelName: 'Supplier', model: supplierModel },
    { modelName: 'wastageModel', model: stockModel },
    { modelName: 'wastageModel', model: wastageModel },
    // { modelName: 'blog', model: blogModel },
    { modelName: 'device', model: deviceModel },
    { modelName: 'siteManagment', model: siteModel },
    // { modelName: 'reciept', model: recieptModel },
    { modelName: 'paymentlist', model: paymentsModel },
    { modelName: 'orderitem', model: orderItemsModel },
    { modelName: 'customer', model: customerModel },
    { modelName: 'order', model: orderModel },
    { modelName: 'loyalty', model: loyalityModel },
    { modelName: 'coupens', model: coupenModel },
    { modelName: 'Modifier', model: Modifier },
    { modelName: 'ingredientCategoryModel', model: ingredientCatModel },
    { modelName: 'ingredientsModel', model: ingredientModel },
    // Add other models here
  ];
  try {
    for (const { modelName, model } of modelNamesAndSchemas) {
      console.log('model', model)
      console.log('modelNames', modelName)
      const Model = mongoose.model(modelName);

      // Delete data for the given userId using deleteMany
      await Model.deleteMany({ userId: userId });

      console.log(`Data for model ${modelName} with userId ${userId} deleted successfully.`);
    }

    const user = await User.findById({ _id: userId });
    console.log('user: ', user);
    if (user) {
      await user.remove();
    }
    console.log(`User data with userId ${userId} deleted successfully.`);
    // res.send(`User data with userId ${userId} deleted successfully.`);

    console.log('User and related data deleted successfully.');
  } catch (error) {
    console.error('Error deleting user:', error);
  }
}
