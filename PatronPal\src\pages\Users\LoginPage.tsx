/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { FcGoogle } from 'react-icons/fc';
import { FaApple } from 'react-icons/fa';  
import { toast } from 'react-toastify';
import Swal from 'sweetalert2';
import type { AppDispatch } from '../../redux-store/store';
import {
  loginCustomer,
  googleLogin,
  appleLogin,
  clearError,
  selectCustomerLoading,
  selectCustomerError,
  selectIsAuthenticated,
  selectAuthInitialized,
  initializeAuth
} from '../../redux-store/slices/customerSlice';
import { Eye, EyeOff } from 'lucide-react';
// import { simulateGoogleAccountsInBrowser } from '../../utils/googleAccountsHelper';

// Types
interface LoginFormData {
  email: string;
  password: string;
}

interface AppleLoginData {
  Email: string;
  appleId: string;
  customToken: string;
  keyId: string;
  authorizationCode: string;
  firstName?: string;
  lastName?: string;
  name: string;
  services: string;
  clientId: string;
}

// Global state management for social logins
interface SocialLoginState {
  google: {
    loaded: boolean;
    initialized: boolean;
    loading: boolean;
  };
  apple: {
    loaded: boolean;
    initialized: boolean;
    loading: boolean;
  };
}

// Global social login manager
class SocialLoginManager {
  private static instance: SocialLoginManager;
  private state: SocialLoginState = {
    google: { loaded: false, initialized: false, loading: false },
    apple: { loaded: false, initialized: false, loading: false }
  };
  private listeners: Set<() => void> = new Set();

  static getInstance(): SocialLoginManager {
    if (!SocialLoginManager.instance) {
      SocialLoginManager.instance = new SocialLoginManager();
    }
    return SocialLoginManager.instance;
  }

  subscribe(listener: () => void): () => void {
    this.listeners.add(listener);
    return () => {
      this.listeners.delete(listener);
    };
  }

  private notify(): void {
    this.listeners.forEach(listener => listener());
  }

  getState(): SocialLoginState {
    return { ...this.state };
  }

  updateState(provider: 'google' | 'apple', updates: Partial<SocialLoginState['google']>): void {
    this.state[provider] = { ...this.state[provider], ...updates };
    this.notify();
  }

  async loadGoogleScript(config: any, handleResponse: (response: any) => void): Promise<void> {
    if (this.state.google.loading) return;
    
    if (window.google?.accounts?.id) {
      this.initializeGoogle(config, handleResponse);
      return;
    }

    if (this.state.google.loaded) return;

    this.updateState('google', { loading: true });

    try {
      const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
      if (existingScript) {
        await this.waitForGoogle();
        this.initializeGoogle(config, handleResponse);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('google', { loaded: true, loading: false });
          resolve();
        };
        script.onerror = () => {
          this.updateState('google', { loading: false });
          reject(new Error('Failed to load Google script'));
        };
      });

      document.head.appendChild(script);
      await loadPromise;
      this.initializeGoogle(config, handleResponse);
    } catch (error) {
      console.error('❌ Error loading Google script:', error);
      this.updateState('google', { loading: false });
      throw error;
    }
  }

  private async waitForGoogle(): Promise<void> {
    let attempts = 0;
    while (!window.google?.accounts?.id && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  private initializeGoogle(config: any, handleResponse: (response: any) => void): void {
    try {
      if (window.google?.accounts?.id && !this.state.google.initialized) {
        window.google.accounts.id.initialize({
          client_id: config.GOOGLE_CLIENT_ID,
          callback: handleResponse,
          auto_select: false,
          cancel_on_tap_outside: true,
          use_fedcm_for_prompt: false,
        });
        this.updateState('google', { initialized: true });
        console.log('✅ Google Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Google Sign-In:', error);
    }
  }

  async loadAppleScript(config: any): Promise<void> {
    if (this.state.apple.loading) return;
    
    if (window.AppleID?.auth) {
      this.initializeApple(config);
      return;
    }

    if (this.state.apple.loaded) return;

    this.updateState('apple', { loading: true });

    try {
      const existingScript = document.querySelector('script[src*="appleid.cdn-apple.com"]');
      if (existingScript) {
        await this.waitForApple();
        this.initializeApple(config);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js';
      script.async = true;
      script.defer = true;
      
      const loadPromise = new Promise<void>((resolve, reject) => {
        script.onload = () => {
          this.updateState('apple', { loaded: true, loading: false });
          resolve();
        };
        script.onerror = () => {
          this.updateState('apple', { loading: false });
          reject(new Error('Failed to load Apple script'));
        };
      });

      document.head.appendChild(script);
      await loadPromise;
      this.initializeApple(config);
    } catch (error) {
      console.error('❌ Error loading Apple script:', error);
      this.updateState('apple', { loading: false });
      throw error;
    }
  }

  private async waitForApple(): Promise<void> {
    let attempts = 0;
    while (!window.AppleID && attempts < 50) {
      await new Promise(resolve => setTimeout(resolve, 100));
      attempts++;
    }
  }

  private initializeApple(config: any): void {
    try {
      if (window.AppleID?.auth && !this.state.apple.initialized) {
        window.AppleID.auth.init({
          clientId: config.APPLE_CLIENT_ID,
          scope: 'name email',
          redirectURI: config.REDIRECT_URI,
          usePopup: true,
          state: JSON.stringify({
            keyId: config.APPLE_KEY_ID,
            name: config.APPLE_NAME,
            services: config.APPLE_SERVICES,
            timestamp: Date.now()
          })
        });
        this.updateState('apple', { initialized: true });
        console.log('✅ Apple Sign-In initialized successfully');
      }
    } catch (error) {
      console.error('❌ Error initializing Apple Sign-In:', error);
    }
  }
}

// Enhanced validation schema with better patterns
const validationSchema = Yup.object({
  email: Yup.string()
    .email('Please enter a valid email address')
    .matches(
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email format'
    )
    .required('Email is required'),
  password: Yup.string()
    .min(6, 'Password must be at least 6 characters')
    .max(50, 'Password must not exceed 50 characters')
    .required('Password is required'),
});

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch<AppDispatch>();

  // Redux selectors
  const isLoading = useSelector(selectCustomerLoading);
  const error = useSelector(selectCustomerError);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authInitialized = useSelector(selectAuthInitialized);
  
  // Local state
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [socialLoading, setSocialLoading] = useState<{
    google: boolean;
    apple: boolean;
  }>({ google: false, apple: false });
  const [socialState, setSocialState] = useState<SocialLoginState>(() =>
    SocialLoginManager.getInstance().getState()
  );
  // Google authentication uses native popup, no custom modal needed

  // Configuration from environment variables with updated fallbacks
  const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID || "************-mc939due2sphq0lgslraliga9h36j8gf.apps.googleusercontent.com";
  const APPLE_CLIENT_ID = import.meta.env.VITE_APPLE_NAME || "patronpalLoginKey";
  const APPLE_KEY_ID = import.meta.env.VITE_APPLE_KEY_ID || "3NP7Y9SS6L";
  const REDIRECT_URI = `${window.location.origin}/auth/apple/callback`;

  const config = {
    GOOGLE_CLIENT_ID,
    APPLE_CLIENT_ID,
    APPLE_NAME: APPLE_CLIENT_ID,
    APPLE_KEY_ID,
    APPLE_SERVICES: import.meta.env.VITE_APPLE_SERVICES || "DeviceCheck,Account & Organizational Data Sharing",
    REDIRECT_URI
  };

  // Initial form values
  const initialValues: LoginFormData = {
    email: location.state?.email || '',
    password: '',
  };

  // Get social login manager instance
  const socialManager = SocialLoginManager.getInstance();

  // Utility functions
  const togglePasswordVisibility = useCallback((): void => {
    setShowPassword((prev) => !prev);
  }, []);

  // JWT token decoder utility
  const decodeJWT = useCallback((token: string) => {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(function(c) {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
          })
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('❌ Error decoding JWT:', error);
      throw new Error('Invalid token format');
    }
  }, []);

  // Enhanced Google response handler
  const handleGoogleResponse = useCallback(async (response: any) => {
    if (!response?.credential) {
      console.error('❌ No credential received from Google');
      Swal.fire({
        title: 'Google Sign-in Failed',
        html: `
          <div style="text-align: center;">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
            <p style="font-size: 16px; color: #333; margin: 0;">No credential received from Google</p>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try signing in again</p>
          </div>
        `,
        icon: 'error',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-popup-custom'
        }
      });
      return;
    }

    try {
      setSocialLoading(prev => ({ ...prev, google: true }));
      console.log('🔄 Processing Google response...');
      
      const userData = decodeJWT(response.credential);
      console.log('✅ Decoded Google user data:', {
        email: userData.email,
        sub: userData.sub,
        name: userData.name
      });

      const result = await dispatch(googleLogin({
        Email: userData.email,
        googleId: userData.sub
      }));

      if (googleLogin.fulfilled.match(result)) {
        // Show SweetAlert2 success message for Google login
        Swal.fire({
          title: 'Welcome Back!',
          html: `
            <div style="text-align: center;">
              <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTJMMTEgMTRMMTUgMTAiIHN0cm9rZT0iIzEwYjk4MSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iIzEwYjk4MSIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+Cjwvc3ZnPgo=" alt="Success" style="width: 64px; height: 64px; margin-bottom: 16px;">
              <p style="font-size: 16px; color: #333; margin: 0;">Successfully signed in with Google!</p>
              <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Redirecting...</p>
            </div>
          `,
          icon: 'success',
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          allowOutsideClick: false,
          allowEscapeKey: false,
          customClass: {
            popup: 'swal2-popup-custom'
          }
        }).then(() => {
          // Handle redirect after Google login
          const urlParams = new URLSearchParams(location.search);
          const redirectParam = urlParams.get('redirect');
          const typeParam = urlParams.get('type');
          const redirectTo = location.state?.from?.pathname || redirectParam || '/';

          // Preserve order type parameter when redirecting to checkout
          if (redirectTo === '/checkout') {
            const checkoutUrl = typeParam ? `/checkout?type=${typeParam}` : '/checkout';
            window.location.href = checkoutUrl;
          } else {
            navigate(redirectTo, { replace: true });
          }
        });
      } else if (googleLogin.rejected.match(result)) {
        const errorMessage = result.payload || 'Google sign-in failed';
        console.error('❌ Google login failed:', errorMessage);

        if (errorMessage.includes('not found') || errorMessage.includes('userNotFound')) {
          // Show SweetAlert2 info message for account not found
          Swal.fire({
            title: 'Account Not Found',
            html: `
              <div style="text-align: center;">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iIzM5OGVmNCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xMiA4VjEyIiBzdHJva2U9IiMzOThlZjQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMiAxNkgxMi4wMSIgc3Ryb2tlPSIjMzk4ZWY0IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="Info" style="width: 64px; height: 64px; margin-bottom: 16px;">
                <p style="font-size: 16px; color: #333; margin: 0;">Account not found with this Google account</p>
                <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Redirecting to signup page...</p>
              </div>
            `,
            icon: 'info',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            customClass: {
              popup: 'swal2-popup-custom'
            }
          }).then(() => {
            navigate(`/signup${location.search}`);
          });
        } else {
          // Show SweetAlert2 error message for other Google login failures
          Swal.fire({
            title: 'Google Sign-in Failed',
            html: `
              <div style="text-align: center;">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
                <p style="font-size: 16px; color: #333; margin: 0;">${errorMessage}</p>
                <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again</p>
              </div>
            `,
            icon: 'error',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            customClass: {
              popup: 'swal2-popup-custom'
            }
          });
        }
      }
    } catch (error) {
      console.error('❌ Google Sign-in error:', error);

      let errorMessage = 'An error occurred during Google Sign-in';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      // Show SweetAlert2 error for catch block
      Swal.fire({
        title: 'Google Sign-in Error',
        html: `
          <div style="text-align: center;">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
            <p style="font-size: 16px; color: #333; margin: 0;">${errorMessage}</p>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or contact support</p>
          </div>
        `,
        icon: 'error',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-popup-custom'
        }
      });
    } finally {
      setSocialLoading(prev => ({ ...prev, google: false }));
    }
  }, [decodeJWT, dispatch, navigate]);

  // Enhanced Apple response handler
  const handleAppleResponse = useCallback(async (response: any) => {
    if (!response?.authorization) {
      console.error('❌ No authorization received from Apple');
      toast.error('No authorization received from Apple');
      return;
    }

    try {
      setSocialLoading(prev => ({ ...prev, apple: true }));
      console.log('🔄 Processing Apple response...');

      // Parse state data
      let stateData = {};
      try {
        if (response.authorization.state) {
          stateData = JSON.parse(response.authorization.state);
        }
      } catch (e) {
        console.warn('⚠️ Could not parse Apple state data:', e);
      }

      // Extract Apple ID from token
      let appleId = '';
      if (response.authorization.id_token) {
        try {
          const decodedToken = decodeJWT(response.authorization.id_token);
          appleId = decodedToken.sub || '';
        } catch (error) {
          console.error('❌ Error decoding Apple ID token:', error);
        }
      }

      const appleLoginData: AppleLoginData = {
        Email: response.user?.email || '',
        appleId: appleId,
        customToken: response.authorization.id_token || '',
        keyId: config.APPLE_KEY_ID,
        authorizationCode: response.authorization.code || '',
        firstName: response.user?.name?.firstName || '',
        lastName: response.user?.name?.lastName || '',
        name: config.APPLE_NAME,
        services: config.APPLE_SERVICES,
        clientId: config.APPLE_CLIENT_ID,
        ...stateData
      };

      console.log('✅ Apple login data prepared:', {
        ...appleLoginData,
        customToken: appleLoginData.customToken ? '[TOKEN_PRESENT]' : '[NO_TOKEN]'
      });

      const result = await dispatch(appleLogin(appleLoginData));

      if (appleLogin.fulfilled.match(result)) {
        toast.success('Successfully signed in with Apple!');
      } else if (appleLogin.rejected.match(result)) {
        const errorMessage = result.payload || 'Apple sign-in failed';
        console.error('❌ Apple login failed:', errorMessage);
        
        if (errorMessage.includes('not found') || errorMessage.includes('userNotFound')) {
          toast.info('Account not found. Please sign up first.');
          navigate(`/signup${location.search}`);
        } else {
          toast.error(errorMessage);
        }
      }
    } catch (error) {
      console.error('❌ Apple Sign-in error:', error);
      
      let errorMessage = 'An error occurred during Apple Sign-in';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error: Unable to connect to server. Please check your internet connection.';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      toast.error(errorMessage);
    } finally {
      setSocialLoading(prev => ({ ...prev, apple: false }));
    }
  }, [decodeJWT, dispatch, navigate, config]);

  // Enhanced form submission
  const handleSubmit = useCallback(async (values: LoginFormData, { setSubmitting }: any) => {
    dispatch(clearError());

    try {
      console.log('🔄 Attempting login for:', values.email);
      
      const result = await dispatch(loginCustomer({
        Email: values.email.trim().toLowerCase(),
        Password: values.password
      }));

      if (loginCustomer.fulfilled.match(result)) {
        toast.success('Successfully logged in!');
        console.log('✅ Login successful');
      } else if (loginCustomer.rejected.match(result)) {
        const errorMessage = result.payload || 'Login failed';
        console.error('❌ Login failed:', errorMessage);
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setSubmitting(false);
    }
  }, [dispatch]);

  // Direct Google Sign-In handler - no loading modal
  const handleGoogleSignIn = useCallback(async () => {
    if (socialLoading.google) {
      return; // Prevent multiple clicks
    }

    try {
      console.log('🔄 Starting Google Sign-In...');

      if (window.google?.accounts?.id) {
        // Initialize Google with popup mode for direct account selection
        window.google.accounts.id.initialize({
          client_id: GOOGLE_CLIENT_ID,
          callback: handleGoogleResponse,
          auto_select: false,
          cancel_on_tap_outside: false,
          ux_mode: 'popup'
        });

        // Create invisible button and trigger it immediately
        const tempContainer = document.createElement('div');
        tempContainer.style.position = 'absolute';
        tempContainer.style.left = '-9999px';
        tempContainer.style.top = '-9999px';
        document.body.appendChild(tempContainer);

        // Render Google button
        window.google.accounts.id.renderButton(tempContainer, {
          theme: 'outline',
          size: 'large',
          type: 'standard',
          shape: 'rectangular',
          text: 'signin_with',
          logo_alignment: 'left',
          width: 300
        });

        // Immediately trigger the button click to open Google's popup
        setTimeout(() => {
          const googleButton = tempContainer.querySelector('div[role="button"]') as HTMLElement;
          if (googleButton) {
            googleButton.click();
            console.log('✅ Triggered Google popup directly');
          } else {
            console.log('❌ Could not find Google button, showing fallback');
            tryAlternativeGoogleSignIn();
          }

          // Clean up the temporary container
          setTimeout(() => {
            if (document.body.contains(tempContainer)) {
              document.body.removeChild(tempContainer);
            }
          }, 1000);
        }, 50); // Reduced delay for faster response

      } else {
        console.log('Google API not available, trying to load...');
        // Try to load Google API if not available
        await loadGoogleAPI();
        // Retry after loading
        setTimeout(() => handleGoogleSignIn(), 300);
      }

    } catch (error) {
      console.error('❌ Error starting Google Sign-In:', error);
      Swal.fire({
        title: 'Failed to Start Google Sign-In',
        html: `
          <div style="text-align: center;">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
            <p style="font-size: 16px; color: #333; margin: 0;">Failed to start Google Sign-In</p>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or refresh the page</p>
          </div>
        `,
        icon: 'error',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-popup-custom'
        }
      });
    }
  }, [socialLoading.google, GOOGLE_CLIENT_ID, handleGoogleResponse]);

  // Load Google API if not available
  const loadGoogleAPI = useCallback(async () => {
    return new Promise<void>((resolve, reject) => {
      if (window.google?.accounts?.id) {
        resolve();
        return;
      }

      // Check if script already exists
      const existingScript = document.querySelector('script[src*="accounts.google.com/gsi/client"]');
      if (existingScript) {
        // Wait for it to load
        let attempts = 0;
        const checkLoaded = () => {
          if (window.google?.accounts?.id || attempts > 50) {
            resolve();
          } else {
            attempts++;
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
        return;
      }

      // Create and load the script
      const script = document.createElement('script');
      script.src = 'https://accounts.google.com/gsi/client';
      script.async = true;
      script.defer = true;

      script.onload = () => {
        console.log('✅ Google API loaded successfully');
        resolve();
      };

      script.onerror = () => {
        console.error('❌ Failed to load Google API');
        reject(new Error('Failed to load Google API'));
      };

      document.head.appendChild(script);
    });
  }, []);

  // Alternative Google Sign-In method using renderButton
  const tryAlternativeGoogleSignIn = useCallback(() => {
    try {
      console.log('🔄 Trying alternative Google Sign-In method...');

      if (!window.google?.accounts?.id) {
        console.error('Google API still not available');
        Swal.fire({
          title: 'Google Sign-In Unavailable',
          html: `
            <div style="text-align: center;">
              <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2Y5NzMxNiIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xMiA4VjEyIiBzdHJva2U9IiNmOTczMTYiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+CjxwYXRoIGQ9Ik0xMiAxNkgxMi4wMSIgc3Ryb2tlPSIjZjk3MzE2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K" alt="Warning" style="width: 64px; height: 64px; margin-bottom: 16px;">
              <p style="font-size: 16px; color: #333; margin: 0;">Google Sign-In is not available</p>
              <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please refresh the page and try again</p>
            </div>
          `,
          icon: 'warning',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          customClass: {
            popup: 'swal2-popup-custom'
          }
        });
        setSocialLoading(prev => ({ ...prev, google: false }));
        return;
      }

      // Re-initialize to ensure fresh state
      window.google.accounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: handleGoogleResponse,
        auto_select: false,
        cancel_on_tap_outside: false,
        ux_mode: 'popup'
      });

      // Create a visible container for Google button (temporarily)
      const tempContainer = document.createElement('div');
      tempContainer.style.position = 'fixed';
      tempContainer.style.top = '50%';
      tempContainer.style.left = '50%';
      tempContainer.style.transform = 'translate(-50%, -50%)';
      tempContainer.style.zIndex = '10000';
      tempContainer.style.backgroundColor = 'white';
      tempContainer.style.padding = '20px';
      tempContainer.style.borderRadius = '8px';
      tempContainer.style.boxShadow = '0 4px 20px rgba(0,0,0,0.3)';
      document.body.appendChild(tempContainer);

      // Add a message
      const message = document.createElement('div');
      message.textContent = 'Click the Google button below to continue:';
      message.style.marginBottom = '10px';
      message.style.textAlign = 'center';
      tempContainer.appendChild(message);

      // Render Google button
      const buttonContainer = document.createElement('div');
      tempContainer.appendChild(buttonContainer);

      window.google.accounts.id.renderButton(buttonContainer, {
        theme: 'outline',
        size: 'large',
        type: 'standard',
        shape: 'rectangular',
        text: 'signin_with',
        logo_alignment: 'left',
        width: 300
      });

      // Add close button
      const closeButton = document.createElement('button');
      closeButton.textContent = 'Cancel';
      closeButton.style.marginTop = '10px';
      closeButton.style.width = '100%';
      closeButton.style.padding = '8px';
      closeButton.style.border = '1px solid #ccc';
      closeButton.style.borderRadius = '4px';
      closeButton.style.backgroundColor = '#f5f5f5';
      closeButton.style.cursor = 'pointer';
      closeButton.onclick = () => {
        if (document.body.contains(tempContainer)) {
          document.body.removeChild(tempContainer);
        }
        setSocialLoading(prev => ({ ...prev, google: false }));
      };
      tempContainer.appendChild(closeButton);

      // Auto-remove after 30 seconds
      setTimeout(() => {
        if (document.body.contains(tempContainer)) {
          document.body.removeChild(tempContainer);
          setSocialLoading(prev => ({ ...prev, google: false }));
        }
      }, 30000);

    } catch (error) {
      console.error('❌ Alternative Google Sign-In failed:', error);
      Swal.fire({
        title: 'Google Sign-In Failed',
        html: `
          <div style="text-align: center;">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiIHN0cm9rZT0iI2VmNDQ0NCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+CjxwYXRoIGQ9Ik0xNSA5TDkgMTVNOSA5TDE1IDE1IiBzdHJva2U9IiNlZjQ0NDQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIi8+Cjwvc3ZnPgo=" alt="Error" style="width: 64px; height: 64px; margin-bottom: 16px;">
            <p style="font-size: 16px; color: #333; margin: 0;">Failed to show Google Sign-In</p>
            <p style="font-size: 14px; color: #666; margin: 8px 0 0 0;">Please try again or contact support</p>
          </div>
        `,
        icon: 'error',
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'swal2-popup-custom'
        }
      });
      setSocialLoading(prev => ({ ...prev, google: false }));
    }
  }, [GOOGLE_CLIENT_ID, handleGoogleResponse]);

  // Google authentication now uses native popup - no custom functions needed

  // Enhanced Apple Sign-In handler
  const handleAppleSignIn = useCallback(async () => {
    if (!socialState.apple.initialized) {
      toast.error('Apple Sign-In is still loading. Please wait a moment and try again.');
      return;
    }

    if (socialLoading.apple) {
      return; // Prevent multiple clicks
    }

    if (window.AppleID?.auth) {
      try {
        console.log('🔄 Attempting Apple Sign-In...');
        const response = await window.AppleID.auth.signIn({
          usePopup: true,
          state: JSON.stringify({
            keyId: config.APPLE_KEY_ID,
            name: config.APPLE_NAME,
            services: config.APPLE_SERVICES,
            timestamp: Date.now()
          })
        });
        console.log('✅ Apple Sign-In response received');
        await handleAppleResponse(response);
      } catch (error) {
        console.error('❌ Error with Apple Sign-In:', error);
        
        // Don't show error for user-cancelled actions
        const errorString = error?.toString() || '';
        if (error !== 'popup_closed_by_user' && 
            !errorString.includes('popup_closed_by_user') &&
            !errorString.includes('user_cancelled_authorize')) {
          toast.error('Failed to sign in with Apple. Please try again.');
        }
      }
    } else {
      console.error('❌ Apple Sign-In not loaded');
      toast.error('Apple Sign-In is not available. Please refresh the page and try again.');
    }
  }, [socialState.apple.initialized, socialLoading.apple, config, handleAppleResponse]);

  // Initialize social logins
  const initializeSocialLogins = useCallback(async () => {
    try {
      const promises = [];
      
      if (!socialState.google.loaded && !socialState.google.loading) {
        promises.push(socialManager.loadGoogleScript(config, handleGoogleResponse));
      }
      
      if (!socialState.apple.loaded && !socialState.apple.loading) {
        promises.push(socialManager.loadAppleScript(config));
      }

      if (promises.length > 0) {
        await Promise.allSettled(promises);
      }
    } catch (error) {
      console.error('❌ Error initializing social logins:', error);
    }
  }, [socialState, config, handleGoogleResponse, socialManager]);

  // Effects
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  useEffect(() => {
    if (authInitialized && isAuthenticated) {
      console.log('✅ User authenticated, redirecting...');
      // Get the redirect URL from location state, URL params, or default to home
      const urlParams = new URLSearchParams(location.search);
      const redirectParam = urlParams.get('redirect');
      const redirectTo = location.state?.from?.pathname || redirectParam || '/';

      // Add a small delay to ensure state is fully updated
      setTimeout(() => {
        // Preserve order type parameter when redirecting to checkout
        if (redirectTo === '/checkout') {
          const urlParams = new URLSearchParams(location.search);
          const typeParam = urlParams.get('type');
          const checkoutUrl = typeParam ? `/checkout?type=${typeParam}` : '/checkout';
          window.location.href = checkoutUrl;
        } else {
          navigate(redirectTo, { replace: true });
        }
      }, 100);
    }
  }, [isAuthenticated, authInitialized, navigate, location.state, location.search]);

  // Subscribe to social login state changes
  useEffect(() => {
    const unsubscribe = socialManager.subscribe(() => {
      setSocialState(socialManager.getState());
    });

    return unsubscribe;
  }, [socialManager]);

  // Initialize social logins only when needed
  useEffect(() => {
    if (authInitialized && !isAuthenticated) {
      initializeSocialLogins();
    }
  }, [authInitialized, isAuthenticated, initializeSocialLogins]);

  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  // Handle messages from navigation state (e.g., from OTP verification)
  useEffect(() => {
    if (location.state?.message) {
      toast.success(location.state.message);
      // Clear the message from state to prevent showing it again
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  // Check if any social login is in progress (excluding Google for direct auth)
  const isSocialLoading = socialLoading.apple; // Removed Google to prevent loading modal

  // Loading state
  if (!authInitialized) {
    return (
      <div className="flex justify-center items-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5C00] mx-auto"></div>
          <p className="mt-4 text-gray-600 text-lg">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-50 p-5">
      {/* Social Loading Modal - Disabled for direct Google authentication */}

      <div className="w-full max-w-md">
        <div className="text-left mb-8">
          
          <h1 className="text-2xl font-bold text-gray-900">
            Patron<span className='text-[#FF5C00]'>Pal</span>
          </h1>
         
          <h2 className="text-xl font-semibold mt-4 mb-2">Login to your Account</h2>
          <p className="text-gray-600 text-sm">
            Welcome back! Please sign in to continue to your account.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm" role="alert">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-2">
                <span>{error}</span>
              </div>
            </div>
          </div>
        )}

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          validateOnChange={true}
          validateOnBlur={true}
        >
          {({ isSubmitting, touched, errors }) => (
            <Form noValidate>
              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <Field
                  type="email"
                  id="email"
                  name="email"
                  autoComplete="username email"
                  placeholder="Enter your email address"
                  className={`w-full px-3 py-2 bg-white border rounded-md focus:outline-none focus:ring-2 transition-colors ${
                    touched.email && errors.email
                      ? 'border-red-300 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-[#FF5C00]'
                  }`}
                  disabled={isLoading || isSubmitting || isSocialLoading}
                />
                <ErrorMessage name="email" component="div" className="text-red-600 text-xs mt-1" />
              </div>

              <div className="mb-2 relative">
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Password *
                </label>
                <Field
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  autoComplete="current-password"
                  placeholder="Enter your password"
                  className={`w-full px-3 py-2 pr-10 border bg-white rounded-md focus:outline-none focus:ring-2 transition-colors ${
                    touched.password && errors.password
                      ? 'border-red-300 focus:ring-red-500'
                      : 'border-gray-300 focus:ring-[#FF5C00]'
                  }`}
                  disabled={isLoading || isSubmitting || isSocialLoading}
                />
                <ErrorMessage name="password" component="div" className="text-red-600 text-xs mt-1" />

                <button
                  type="button"
                  onClick={togglePasswordVisibility}
                  className="absolute right-3 top-[38px] text-gray-500 hover:text-gray-800 focus:outline-none transition-colors"
                  tabIndex={-1}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                  disabled={isSocialLoading}
                >
                  {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                </button>
              </div>

              <div className="flex justify-end mb-6">
                <Link 
                  to="/forget-password" 
                  className="text-sm text-[#FF5C00] hover:text-[#E54E00] transition-colors focus:outline-none focus:underline"
                >
                  Forgot password?
                </Link>
              </div>

              <button
                type="submit"
                disabled={isLoading || isSubmitting || isSocialLoading}
                className="w-full bg-[#FF5C00] text-white py-2 rounded-md hover:bg-[#E54E00] transition-colors focus:outline-none focus:ring-2 focus:ring-[#FF5C00] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
              >
                {isLoading || isSubmitting ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </span>
                ) : (
                  'Sign in'
                )}
              </button>
            </Form>
          )}
        </Formik>

        <div className="mt-6 text-center">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-gray-50 text-gray-500">Or continue with</span>
            </div>
          </div>
        </div>

        {/* Social Login Buttons */}
        <div className="mt-6 space-y-3">
          <button
            id="google-signin-button"
            onClick={handleGoogleSignIn}
            disabled={isLoading || !socialState.google || isSocialLoading}
            className="group relative flex items-center justify-center w-full px-4 py-3 bg-white rounded-xl text-sm font-medium text-gray-700 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 cursor-pointer border-0"
          >
            <div className="flex items-center">
              <FcGoogle className="w-5 h-5 mr-3" />
              <span className="group-hover:text-gray-900 transition-colors">
                {!socialState.google ? 'Loading Google...' : 'Continue with Google'}
              </span>
            </div>
          </button>

          <button
            onClick={handleAppleSignIn}
            disabled={isLoading || socialLoading.apple || !socialState.apple || isSocialLoading}
            className="flex items-center justify-center w-full px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#FF5C00] disabled:opacity-50 disabled:cursor-not-allowed transition-colors cursor-pointer"
          >
            <FaApple className="w-5 h-5 mr-2" />
            {!socialState.apple.initialized ? 'Loading Apple...' : 'Continue with Apple ID'}
          </button>
        </div>

       <div className="mt-6 text-center space-y-4">
  <p className="text-sm text-gray-600">
    Don't have an account?{' '}
    <Link
      to={`/signup${location.search}`}
      className="text-[#FF5C00] hover:text-[#E54E00] font-medium transition-colors focus:outline-none focus:underline"
    >
      Sign up
    </Link>
    <p className="text-sm text-gray-600 pl-5">OR
  </p>
   <button
    onClick={() => navigate("/")}
    className="text-sm text-gray-600 hover:text-[#FF5C00] transition-colors"
  >
    go to <span className= "text-orange-500">Home</span>
  </button>
  </p> 
  
 
</div>
      </div>

      {/* Google's native account picker will be shown automatically */}
    </div>
  );
};

export default LoginPage;