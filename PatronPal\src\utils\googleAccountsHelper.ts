// Google Accounts Helper - for testing real account detection

export const storeGoogleAccountsForTesting = (accounts: string[]) => {
  try {
    localStorage.setItem('google_accounts', JSON.stringify(accounts));
    console.log('✅ Stored Google accounts for testing:', accounts);
  } catch (error) {
    console.error('❌ Failed to store Google accounts:', error);
  }
};

export const getStoredGoogleAccounts = (): string[] => {
  try {
    const stored = localStorage.getItem('google_accounts');
    if (stored) {
      const accounts = JSON.parse(stored);
      return Array.isArray(accounts) ? accounts : [];
    }
  } catch (error) {
    console.error('❌ Failed to get stored Google accounts:', error);
  }
  return [];
};

export const clearStoredGoogleAccounts = () => {
  try {
    localStorage.removeItem('google_accounts');
    sessionStorage.removeItem('google_accounts');
    console.log('✅ Cleared stored Google accounts');
  } catch (error) {
    console.error('❌ Failed to clear stored Google accounts:', error);
  }
};

// Function to simulate having Google accounts in browser
export const simulateGoogleAccountsInBrowser = () => {
  const testAccounts = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  storeGoogleAccountsForTesting(testAccounts);
  
  // Also try to set in sessionStorage
  try {
    sessionStorage.setItem('google_accounts', JSON.stringify(testAccounts));
  } catch (error) {
    console.warn('Could not set sessionStorage');
  }
  
  console.log('🔧 Simulated Google accounts in browser. Try the Google sign-in now!');
  return testAccounts;
};

// Add to window for easy testing in console
if (typeof window !== 'undefined') {
  (window as any).simulateGoogleAccounts = simulateGoogleAccountsInBrowser;
  (window as any).clearGoogleAccounts = clearStoredGoogleAccounts;
  (window as any).getGoogleAccounts = getStoredGoogleAccounts;
}
