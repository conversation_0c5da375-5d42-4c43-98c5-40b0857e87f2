import parentOnlineOrder from '../models/parentOnline-order.js'
import OnlineOrderItem from '../models/subOnline-OrderItem.js'
import PatronPalCustomer from '../models/patronpalCustomers.js'
import sendMail from '../middlewares/send-email.js'

// export const GetparentOnlineOrder = async (req, res) => {
//     const onlineOrderData = await parentOnlineOrder.find().populate('subOnlineOrderId').populate('userId').populate('customerId');
//     res.send(onlineOrderData)
// }
// export const GetparentOnlineOrderById = async (req, res) => {
//     const parentOnlineOrders = await parentOnlineOrder.find(req.params).populate('subOnlineOrderId').populate('userId').populate('customerId');
//     res.send(parentOnlineOrders)
// }


export const GetparentOnlineOrder = async (req, res) => {
    try {
        const onlineOrderData = await parentOnlineOrder.find()
            .populate('userId')
            .populate('customerId');

        // Populate the subOnlineOrderId array for each parent order
        for (let order of onlineOrderData) {
            if (order.subOnlineOrderId && order.subOnlineOrderId.length > 0) {
                order.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: order.subOnlineOrderId } })
                    .populate('customerId')
                    .populate('userId');
            }
        }

        res.send(onlineOrderData);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

    // export const GetparentOnlineOrderById = async (req, res) => {
    //     try {
    //         const { id } = req.params;
    //         const ParentOnlineOrder = await parentOnlineOrder.findById(id)
    //             .populate('userId')
    //             .populate('customerId');

    //             console.log("ParentOnlineOrder :    ",ParentOnlineOrder);
                
    //         // Populate the subOnlineOrderId array for the parent order
    //         if (ParentOnlineOrder.subOnlineOrderId && ParentOnlineOrder.subOnlineOrderId.length > 0) {
    //             ParentOnlineOrder.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: ParentOnlineOrder.subOnlineOrderId } })
    //                 .populate('customerId')
    //                 .populate('userId');
    //         }

    //         res.send(ParentOnlineOrder);
    //     } catch (error) {
    //         res.status(500).json({ message: error.message });
    //     }
    // };



export const GetparentOnlineOrderById = async (req, res) => {
    try {
        const { customerId } = req.params;

        // Find all orders with the given customerId
        const orders = await parentOnlineOrder.find({ customerId })
            .populate('customerId')
            .populate('subOnlineOrderId');

        // Populate the subOnlineOrderId array for each parent order
        const populatedOrders = await Promise.all(orders.map(async order => {
            if (order.subOnlineOrderId && order.subOnlineOrderId.length > 0) {
                order.subOnlineOrderId = await OnlineOrderItem.find({ _id: { $in: order.subOnlineOrderId } })
                    .populate('customerId')
                    .populate('userId');
            }
            return order;
        }));

        res.send(populatedOrders);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};


// export const PostparentOnlineOrder = async (req, res) => {
//     const { customerId, subOnlineOrderId, totalAmount } = req.body;
//     const lastOrder = await parentOnlineOrder.findOne({ customerId }, {}, { sort: { '_id': -1 } });
//     const lastOrderCount = lastOrder ? (lastOrder.OrderNo || 0) : 0;
//     let numericCount
//     if (lastOrderCount != 0) {

//         numericCount = parseInt(lastOrderCount.slice(2), 10) + 1;
//     } else {

//         numericCount = Number("00001")
//     }
//     const OrderNo = `OR${numericCount.toString().padStart(4, '0')}`
//     const data = new parentOnlineOrder({ OrderNo, customerId, subOnlineOrderId,totalAmount})

//     await data.save().then(async (result) => {
//         res.json({
//             OrderNo: result.OrderNo,
//             subOnlineOrderId:result.subOnlineOrderId,
//             customerId: result.customerId,
//             totalAmount: result.totalAmount
//         })
//     })
// }


export const PostparentOnlineOrder = async (req, res) => {
    try {
        const { customerId, subOnlineOrderId, totalAmount } = req.body;
        // Find the last order globally (not just for this customer) to get the correct sequential number
        const lastOrder = await parentOnlineOrder.findOne({}, {}, { sort: { '_id': -1 } });
        const lastOrderCount = lastOrder ? (lastOrder.OrderNo || 0) : 0;
        let numericCount;

        if (lastOrderCount !== 0) {
            numericCount = parseInt(lastOrderCount.slice(2), 10) + 1;
        } else {
            numericCount = Number("00001");
        }

        const OrderNo = `OR${numericCount.toString().padStart(4, '0')}`;

        console.log('Order Number Generation Debug:');
        console.log('Last Order:', lastOrder ? lastOrder.OrderNo : 'No previous orders');
        console.log('Last Order Count:', lastOrderCount);
        console.log('Numeric Count:', numericCount);
        console.log('Generated Order No:', OrderNo);

        const data = new parentOnlineOrder({ OrderNo, customerId, subOnlineOrderId, totalAmount });

        await data.save().then(async (result) => {
            // Update all sub-orders with the correct parent order number
            const updateResult = await OnlineOrderItem.updateMany(
                { _id: { $in: subOnlineOrderId } },
                { $set: { orderNo: OrderNo } }
            );
            console.log(`Updated ${updateResult.modifiedCount} sub-orders with order number: ${OrderNo}`);

            const onlineOrderItems = await OnlineOrderItem.find({ _id: { $in: subOnlineOrderId } })
                .populate({path: 'customerId',model:'PatronPalCustomer'})
                .populate('userId');

            const populatedParentOrder = await parentOnlineOrder.findById(result._id)
                .populate({path: 'customerId', model: 'PatronPalCustomer'});

            if (populatedParentOrder.customerId) {
                console.log("populatedParentOrder.customerId : ", populatedParentOrder.customerId);

                const customerdata = await PatronPalCustomer.findByIdAndUpdate(
                    populatedParentOrder.customerId,
                    {
                        $inc: { "CustomerLoyalty.Points": 5 }
                    },
                    { new: true }  // This option returns the updated document
                );

                console.log("customerdata : ", customerdata);

                // Send order confirmation email
                try {
                    const customer = populatedParentOrder.customerId;
                    const customerEmail = customer.Email; // Note: Capital E for Email field
                    const customerName = `${customer.FirstName} ${customer.LastName}`; // Note: Capital F and L

                    // Create order items list for email and calculate totals
                    let orderItemsHtml = '';
                    let totalItems = 0;
                    let subtotal = 0;
                    let totalTaxAmount = 0;

                    onlineOrderItems.forEach(orderItem => {
                        if (orderItem.product && Array.isArray(orderItem.product)) {
                            orderItem.product.forEach(item => {
                                const itemTotal = (item.price || 0) * (item.quantity || 1);
                                totalItems += item.quantity || 1;
                                subtotal += itemTotal;

                                orderItemsHtml += `
                                    <tr style="border-bottom: 1px solid #eee;">
                                        <td style="padding: 12px; text-align: left;">${item.name || 'Item'}</td>
                                        <td style="padding: 12px; text-align: center;">${item.quantity || 1}</td>
                                        <td style="padding: 12px; text-align: right;">$${(item.price || 0).toFixed(2)}</td>
                                        <td style="padding: 12px; text-align: right;">$${itemTotal.toFixed(2)}</td>
                                    </tr>
                                `;
                            });
                        }

                        // Calculate tax for this order item
                        if (orderItem.tax && Array.isArray(orderItem.tax)) {
                            orderItem.tax.forEach(taxItem => {
                                totalTaxAmount += taxItem.addtax || 0;
                            });
                        }
                    });

                    // If tax amount is still 0, calculate it from the difference between total and subtotal
                    if (totalTaxAmount === 0 && populatedParentOrder.totalAmount > subtotal) {
                        totalTaxAmount = parseFloat((populatedParentOrder.totalAmount - subtotal).toFixed(2));
                    }

                    const emailSubject = `Order Confirmation - ${populatedParentOrder.OrderNo}`;
                    const emailContent = `
                        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #ffffff;">
                            <!-- Header -->
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
                                <h1 style="color: white; margin: 0; font-size: 28px;">Order Confirmed!</h1>
                                <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Thank you for your order</p>
                            </div>

                            <!-- Content -->
                            <div style="padding: 30px;">
                                <p style="font-size: 16px; color: #333; margin-bottom: 20px;">
                                    Hi ${customerName},
                                </p>

                                <p style="font-size: 16px; color: #333; line-height: 1.6; margin-bottom: 25px;">
                                    Great news! Your order has been successfully placed and is being prepared.
                                    Here are your order details:
                                </p>

                                <!-- Order Details Box -->
                                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                                    <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Order Details</h3>
                                    <p style="margin: 5px 0; color: #666;"><strong>Order Number:</strong> ${populatedParentOrder.OrderNo}</p>
                                    <p style="margin: 5px 0; color: #666;"><strong>Total Items:</strong> ${totalItems}</p>
                                    <p style="margin: 5px 0; color: #666;"><strong>Order Date:</strong> ${new Date().toLocaleDateString()}</p>
                                </div>

                                <!-- Order Items -->
                                <div style="margin-bottom: 25px;">
                                    <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Your Items</h3>
                                    <table style="width: 100%; border-collapse: collapse; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                        <thead>
                                            <tr style="background-color: #667eea; color: white;">
                                                <th style="padding: 15px; text-align: left; font-weight: 600;">Item</th>
                                                <th style="padding: 15px; text-align: center; font-weight: 600;">Qty</th>
                                                <th style="padding: 15px; text-align: right; font-weight: 600;">Unit Price</th>
                                                <th style="padding: 15px; text-align: right; font-weight: 600;">Total</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${orderItemsHtml}
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Order Summary -->
                                <div style="background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin-bottom: 25px;">
                                    <h3 style="color: #333; margin: 0 0 15px 0; font-size: 18px;">Order Summary</h3>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0;">
                                        <span style="color: #666; font-size: 16px;">Subtotal:</span>
                                        <span style="color: #333; font-size: 16px; font-weight: 500;">$${subtotal.toFixed(2)}</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0;">
                                        <span style="color: #666; font-size: 16px;">Tax:</span>
                                        <span style="color: #333; font-size: 16px; font-weight: 500;">$${totalTaxAmount.toFixed(2)}</span>
                                    </div>
                                    <div style="border-top: 2px solid #667eea; padding-top: 10px; margin-top: 10px;">
                                        <div style="display: flex; justify-content: space-between;">
                                            <span style="color: #333; font-size: 18px; font-weight: 600;">Total Amount:</span>
                                            <span style="color: #667eea; font-size: 18px; font-weight: 600;">$${populatedParentOrder.totalAmount.toFixed(2)}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Call to Action -->
                                <div style="text-align: center; margin: 30px 0;">
                                    <a href="https://patronpal.com/"
                                       style="display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                              color: white; text-decoration: none; padding: 15px 30px;
                                              border-radius: 25px; font-weight: 600; font-size: 16px;
                                              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);">
                                        Visit PatronPal
                                    </a>
                                </div>

                                <p style="font-size: 14px; color: #666; line-height: 1.6; margin-top: 25px;">
                                    We'll notify you when your order is ready for pickup/delivery.
                                    If you have any questions, feel free to contact our support team.
                                </p>

                                <p style="font-size: 16px; color: #333; margin-top: 25px;">
                                    Thank you for choosing PatronPal!<br>
                                    <span style="color: #667eea; font-weight: 600;">The PatronPal Team</span>
                                </p>
                            </div>

                            <!-- Footer -->
                            <div style="background-color: #f8f9fa; padding: 20px; text-align: center; border-top: 1px solid #eee;">
                                <p style="margin: 0; font-size: 12px; color: #999;">
                                    © 2024 PatronPal. All rights reserved.<br>
                                    <a href="https://patronpal.com/" style="color: #667eea; text-decoration: none;">Visit our website</a>
                                </p>
                            </div>
                        </div>
                    `;

                    // Send the email
                    await sendMail(customerEmail, emailSubject, emailContent);
                    console.log(`Order confirmation email sent to ${customerEmail} for order ${populatedParentOrder.OrderNo}`);

                } catch (emailError) {
                    console.error('Error sending order confirmation email:', emailError);
                    // Don't fail the order creation if email fails
                }
            }

            res.json({
                OrderNo: populatedParentOrder.OrderNo,
                subOnlineOrderId: populatedParentOrder.subOnlineOrderId,
                customerId: populatedParentOrder.customerId,
                totalAmount: populatedParentOrder.totalAmount,
                onlineOrderItems: onlineOrderItems // Include the fetched items in the response
            });
        });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};



export const updateparentOnlineOrder = async (req, res) => {
    try {
        const updatedData = await parentOnlineOrder.findByIdAndUpdate(
            { _id: req.params._id },
            { $set: req.body },
            { new: true }
        );

        if (updatedData) {
            res.send({ message: "parent-Online Orderitem data updated successfully", updatedData });
        } else {
            res.send({ message: "parent-Online Orderitem data cannot be updated successfully", updatedData });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send({ message: "Internal server error" });
    }
}
export const deleteparentOnlineOrder = async (req, res) => {
    try {
        const deletedData = await parentOnlineOrder.findByIdAndDelete(
            { _id: req.params._id },
            { $set: req.body },
            { new: true }
        );

        if (updatedData) {
            res.send({ message: "parent-Online Orderitem data deleted successfully", deletedData });
        } else {
            res.send({ message: "parent-Online Orderitem data cannot be deleted successfully", deletedData });
        }
    } catch (error) {
        console.error(error);
        res.status(500).send({ message: "Internal server error" });
    }
}