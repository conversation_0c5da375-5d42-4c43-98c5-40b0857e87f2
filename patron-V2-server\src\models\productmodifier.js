import mongoose from 'mongoose';

const ModifierSchema = new mongoose.Schema({
    Modifier: [{
        name: {
            type: String
        },
        properties: [{
            name: String,

            totalQuantity: Number,

            price: Number
        }]
    }],
    isActive:{
        type:Boolean
    },
    productId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'product'
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref: 'user'
    }
});
const Modifier = mongoose.model('Modifier', ModifierSchema);
export { Modifier };