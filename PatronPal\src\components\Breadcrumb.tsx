import { Link, useNavigate } from "react-router-dom";
import { ChevronRight, ArrowLeft } from "lucide-react";

interface BreadcrumbProps {
  items: { label: string; path: string }[];
}

// example use
//  const breadcrumbItems = [
//     { label: "Home", path: "/" },
//     { label: "Chicago", path: "/chicago" },
//     { label: restaurantData.name, path: `/restaurant/${restaurantData.id}` },
//   ];
//   ];

{/* <div>
    <Breadcrumb items={breadcrumbItems} />
</div> */}

const Breadcrumb = ({ items }: BreadcrumbProps) => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto px-4 pt-4.5">
      <nav className="flex items-center" aria-label="Breadcrumb">
        {/* Back button - hidden on mobile, visible on tablet and above */}
        <button
          onClick={() => navigate(-1)}
          className="hidden md:flex items-center justify-center w-8 h-8 bg-orange-500 text-white hover:bg-orange-600 rounded-full transition-colors mr-4"
          title="Go back"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>

        <ol className="inline-flex items-center space-x-1 md:space-x-2">
          {items.map((item, index) => (
            <li key={item.path} className="inline-flex items-center">
              {index > 0 && (
                <ChevronRight className="mx-1 h-4 w-4 text-gray-400" />
              )}
              <Link
                to={item.path}
                className={`text-base ${
                  index === items.length - 1
                    ? "text-gray-500 font-medium"
                    : "text-gray-700 hover:text-gray-900"
                }`}
              >
                {item.label}
              </Link>
            </li>
          ))}
        </ol>
      </nav>
    </div>
  );
};

export default Breadcrumb;