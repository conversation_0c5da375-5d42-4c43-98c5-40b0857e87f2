import express from 'express';

import {
  deleteBillDenomination,
  getBillDenomination,
  getBillDenominationById,
  postBillDenomination,
  updateBillDenomination,
  getBillDenominationsByUserId,
  getCashDrawerReport
} from '../api/denomination.js'; // Import the corresponding API functions for Bill Denominations

const routes = express.Router();

// Define routes for Bill Denominations
routes.get('/billdenomination', getCashDrawerReport);
routes.get('/billdenomination/:_id', getBillDenominationById);

routes.post('/billdenomination', postBillDenomination);
routes.put('/billdenomination/:_id', updateBillDenomination);
routes.delete('/billdenomination/:_id', deleteBillDenomination);
routes.get('/billdenomination/:userId', getBillDenominationsByUserId); 

export default routes;
