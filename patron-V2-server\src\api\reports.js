import orderitem from '../models/orderitem.js';
import dotenv from 'dotenv'
import moment from 'moment';
import mongoose from 'mongoose';
import Stripe from 'stripe';

dotenv.config();
if (process.env.NODE_ENV === 'production') {
    var stripe = Stripe('***********************************************************************************************************');
} else if (process.env.NODE_ENV === 'development') {
    var stripe = Stripe('***********************************************************************************************************');
    // var stripe = Stripe('sk_test_51MiZTVF1YkHoz4Y5AsHfg9ovHa5zsRFHCfVrHSy5XKvxKtdKSMHpzQ5V0wEfcGHVfoEQ50NjXhCP0aF2aC1Mc05300eCAJlRxu');
}

export const getItemsSalesLastMonth = async (req, res) => {
    const {
        userId,
        status,
        employeeId,
        customerId,
    } = req.query;

    if (!userId) return res.status(400).send("User ID is required");

    try {
        const startOfDay = moment().startOf("day").toDate();
        const now = new Date();

        const matchStage = {
            userId: new mongoose.Types.ObjectId(userId),
            createdAt: { $gte: startOfDay, $lte: now },
            orderStatus: { $ne: "online" },
        };

        // Employee filtering
        const employeeIdArray = typeof employeeId === "string"
            ? employeeId.split(",")
            : Array.isArray(employeeId) ? employeeId : [];

        const validEmployeeIds = employeeIdArray.filter((id) =>
            mongoose.Types.ObjectId.isValid(id)
        );

        if (validEmployeeIds.length === 1) {
            matchStage.employeeId = new mongoose.Types.ObjectId(validEmployeeIds[0]);
        } else if (validEmployeeIds.length > 1) {
            matchStage.employeeId = {
                $in: validEmployeeIds.map((id) => new mongoose.Types.ObjectId(id)),
            };
        }

        if (customerId) {
            const customerIdArray = typeof customerId === "string"
                ? customerId.split(",")
                : Array.isArray(customerId) ? customerId : [];

            const validCustomerIds = customerIdArray.filter((id) =>
                mongoose.Types.ObjectId.isValid(id)
            );

            if (validCustomerIds.length === 1) {
                matchStage.customerId = new mongoose.Types.ObjectId(validCustomerIds[0]);
            } else if (validCustomerIds.length > 1) {
                matchStage.customerId = {
                    $in: validCustomerIds.map((id) => new mongoose.Types.ObjectId(id)),
                };
            }
        }

        const pipelineBase = [
            { $match: matchStage },
            {
                $addFields: {
                    orderTotalPrice: { $ifNull: ["$grandTotal", 0] },
                    totalDiscount: {
                        $add: [
                            { $ifNull: ["$discountAmount", 0] },
                            { $ifNull: ["$couponOfferAmount", 0] },
                        ],
                    },
                    isRefund: { $ifNull: ["$isRefund", false] }
                },
            },
        ];


        if (status === "Total Sales") {
            pipelineBase.push(
                {
                    $lookup: {
                        from: "paymentlists",
                        localField: "paymentType",
                        foreignField: "_id",
                        as: "paymentInfo",
                    },
                },
                {
                    $unwind: {
                        path: "$paymentInfo",
                        preserveNullAndEmptyArrays: true,
                    },
                },
                {
                    $addFields: {
                        paymentTypeName: {
                            $ifNull: ["$paymentInfo.name", "Cash"],
                        },
                    },
                },
                {
                    $group: {
                        _id: "$paymentTypeName",
                        totalOrders: { $sum: 1 },
                        totalRevenue: { $sum: "$orderTotalPrice" },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        paymentType: "$_id",
                        totalOrders: 1,
                        totalRevenue: 1,
                    },
                }
            );

            const result = await orderitem.aggregate(pipelineBase);
            return res.send({ totalsales: result });
        }
        if (status === "Transactions Summary") {
            const pipeline = [
                ...pipelineBase,
                {
                    $facet: {
                        transactionSummary: [
                            {
                                $group: {
                                    _id: "$category",
                                    transactionCount: { $sum: 1 },
                                    transactionAmount: { $sum: "$orderTotalPrice" },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    category: "$_id",
                                    transactionCount: 1,
                                    transactionAmount: 1,
                                },
                            },
                        ],
                        totalTransactions: [
                            {
                                $group: {
                                    _id: null,
                                    count: { $sum: 1 },
                                    totalAmount: { $sum: "$orderTotalPrice" },
                                },
                            },
                        ],
                        discountSummary: [
                            {
                                $group: {
                                    _id: null,
                                    totalDiscountApplied: { $sum: "$totalDiscount" },
                                    transactionsWithDiscount: {
                                        $sum: {
                                            $cond: [{ $gt: ["$totalDiscount", 0] }, 1, 0],
                                        },
                                    },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    totalDiscountApplied: 1,
                                    transactionsWithDiscount: 1,
                                },
                            },
                        ],
                        refundSummary: [
                            {
                                $match: { isRefund: true },
                            },
                            {
                                $group: {
                                    _id: null,
                                    refundOrders: { $sum: 1 },
                                    refundAmount: { $sum: "$orderTotalPrice" },
                                },
                            },
                            {
                                $project: {
                                    _id: 0,
                                    refundOrders: 1,
                                    refundAmount: 1,
                                },
                            },
                        ],
                    },
                },
            ];

            const [result] = await orderitem.aggregate(pipeline);

            return res.send({
                transactions: {
                    breakdown: result.transactionSummary || [],
                    count: result.totalTransactions[0]?.count || 0,
                    amount: result.totalTransactions[0]?.totalAmount || 0,
                },
                discounts: {
                    count: result.discountSummary[0]?.transactionsWithDiscount || 0,
                    total: result.discountSummary[0]?.totalDiscountApplied || 0,
                },
                refunds: {
                    count: result.refundSummary[0]?.refundOrders || 0,
                    amount: result.refundSummary[0]?.refundAmount || 0,
                },
            });
        }

        if (status === "Discounts & Adjustments") {
            const pipeline = [
                ...pipelineBase,
                {
                    $facet: {
                        couponDiscounts: [
                            {
                                $match: { couponOfferAmount: { $gt: 0 } }
                            },
                            {
                                $group: {
                                    _id: null,
                                    count: { $sum: 1 },
                                    totalAmount: { $sum: "$couponOfferAmount" }
                                }
                            },
                            {
                                $project: {
                                    _id: 0,
                                    discountType: "Coupon Discount",
                                    field: { $literal: "couponOfferAmount" },
                                    count: 1,
                                    amount: "$totalAmount"
                                }
                            }
                        ],
                        embeddedDiscounts: [
                            {
                                $match: {
                                    discountAmount: { $gt: 0 },
                                    $or: [
                                        { "discountType.discountType": { $type: "string" } },
                                        { "discountType.discoutType": { $type: "string" } }
                                    ]
                                }
                            },
                            {
                                $addFields: {
                                    unifiedDiscountType: {
                                        $ifNull: ["$discountType.discountType", "$discountType.discoutType"]
                                    }
                                }
                            },
                            {
                                $addFields: {
                                    normalizedType: {
                                        $trim: {
                                            input: { $toLower: "$unifiedDiscountType" }
                                        }
                                    }
                                }
                            },
                            {
                                $group: {
                                    _id: "$normalizedType",
                                    originalType: { $first: "$unifiedDiscountType" },
                                    count: { $sum: 1 },
                                    totalAmount: { $sum: "$discountAmount" }
                                }
                            },
                            {
                                $project: {
                                    _id: 0,
                                    discountType: "$originalType",
                                    field: { $literal: "discountAmount" },
                                    count: "$count",
                                    amount: "$totalAmount"
                                }
                            }
                        ]
                    }
                },
                {
                    $project: {
                        discounts: {
                            $concatArrays: ["$couponDiscounts", "$embeddedDiscounts"]
                        }
                    }
                },
                { $unwind: "$discounts" },
                {
                    $group: {
                        _id: null,
                        allDiscounts: { $push: "$discounts" },
                        totalDiscountAmount: { $sum: "$discounts.amount" },
                        totalDiscountCount: { $sum: "$discounts.count" }
                    }
                },
                {
                    $project: {
                        _id: 0,
                        discounts: "$allDiscounts",
                        totalDiscountAmount: { $round: ["$totalDiscountAmount", 2] },
                        totalDiscountCount: 1
                    }
                }

            ];


            const result = await orderitem.aggregate(pipeline);

            return res.send({
                discountsAndAdjustments: result || [],
            });
        }

        if (status === "Voids & Refunds") {
            const pipeline = [
                ...pipelineBase,

                {
                    $match: {
                        refundData: { $exists: true, $ne: [] }
                    }
                },

                { $unwind: "$refundData" },

                {
                    $addFields: {
                        refundAmount: { $ifNull: ["$refundTotal", 0] }
                    }
                },

                {
                    $group: {
                        _id: "$_id",
                        OrderNumber: { $first: "$OrderNumber" },
                        createdAt: { $first: "$createdAt" },
                        totalRefundAmount: { $sum: "$refundAmount" }
                    }
                },

                {
                    $group: {
                        _id: null,
                        list: {
                            $push: {
                                OrderNumber: "$OrderNumber",
                                createdAt: "$createdAt",
                                refundAmount: { $round: ["$totalRefundAmount", 2] }
                            }
                        },
                        count: { $sum: 1 },
                        refundTotal: { $sum: "$totalRefundAmount" }
                    }
                },

                {
                    $project: {
                        _id: 0,
                        list: 1,
                        count: 1,
                        refundTotal: { $round: ["$refundTotal", 2] }
                    }
                }
            ];

            const [result] = await orderitem.aggregate(pipeline);

            return res.send({
                refundOrders: result?.list || [],
                count: result?.count || 0,
                refundTotal: result?.refundTotal || 0
            });
        }

        if (validEmployeeIds.length > 0) {
            const pipeline = [
                ...pipelineBase,
                {
                    $project: {
                        _id: 1,
                        createdAt: 1,
                        employeeId: 1,
                        totalPrice: "$orderTotalPrice",
                        totalDiscount: 1,
                    },
                },
            ];

            const employeeData = await orderitem.aggregate(pipeline);
            return res.send({ employeeOrders: employeeData });
        }

        const pipeline = [
            ...pipelineBase,
            {
                $facet: {
                    summary: [
                        {
                            $group: {
                                _id: null,
                                totalOrders: { $sum: 1 },
                                totalRevenue: { $sum: "$orderTotalPrice" },
                                totalDiscount: { $sum: "$totalDiscount" },
                            },
                        },
                        {
                            $project: {
                                _id: 0,
                                totalOrders: 1,
                                totalRevenue: 1,
                                totalDiscount: 1,
                            },
                        },
                    ],
                },
            },
        ];

        const [result] = await orderitem.aggregate(pipeline);
        return res.send({
            summary: result.summary[0] || {
                totalOrders: 0,
                totalRevenue: 0,
                totalDiscount: 0,
            },
        });
    } catch (error) {
        console.error(error);
        res.status(500).send("Internal Server Error");
    }
};

export const getItemsSalesByProduct = async (req, res) => {
    const {
        userId,
        employeeId,
        customerId,
        status,
        startDate,
        endDate
    } = req.query;

    if (!userId) return res.status(400).send("User ID is required");

    try {
        // const startOfDay = moment().startOf("day").toDate();
        // const now = new Date();
        // const matchStage = {
        //     userId: new mongoose.Types.ObjectId(userId),
        //     createdAt: { $gte: startOfDay, $lte: now },
        //     orderStatus: { $ne: "online" },
        // };

        // Default to last 7 full days
        // const start = startDate
        //     ? new Date(startDate)
        //     : moment().subtract(7, "days").startOf("day").toDate();

        // const end = endDate
        //     ? new Date(endDate)
        //     : moment().endOf("day").toDate();

        
        // let dateFilter = {};
        // if (startDate || endDate) {
        //     // Custom date range provided
        //     if (startDate) {
        //         dateFilter.$gte = moment(startDate).startOf("day").toDate();
        //     }
        //     if (endDate) {
        //         dateFilter.$lte = moment(endDate).endOf("day").toDate();
        //     }
        // } else {
        //     // Default to today if no date range provided
        //     dateFilter = { 
        //         $gte: moment().startOf("day").toDate(),
        //         $lte: moment().endOf("day").toDate() 
        //     };
        // }

        // const matchStage = {
        //     userId: new mongoose.Types.ObjectId(userId),
        //     createdAt: dateFilter,
        //     orderStatus: { $ne: "online" },
        // };

        let dateFilter = {};
if (startDate || endDate) {
    // Custom date range provided
    if (startDate) {
        dateFilter.$gte = moment(startDate).startOf("day").toDate();
    }
    if (endDate) {
        dateFilter.$lte = moment(endDate).endOf("day").toDate();
    }
} else {
    // Default to last 29 days + today (30 days total) if no date range provided
    dateFilter = { 
        $gte: moment().subtract(29, "days").startOf("day").toDate(),
        $lte: moment().endOf("day").toDate() 
    };
}

const matchStage = {
    userId: new mongoose.Types.ObjectId(userId),
    createdAt: dateFilter,
    orderStatus: { $ne: "online" },
};

        const employeeIdArray = typeof employeeId === "string"
            ? employeeId.split(",")
            : Array.isArray(employeeId) ? employeeId : [];

        const validEmployeeIds = employeeIdArray.filter(id =>
            mongoose.Types.ObjectId.isValid(id)
        );

        if (validEmployeeIds.length === 1) {
            matchStage.employeeId = new mongoose.Types.ObjectId(validEmployeeIds[0]);
        } else if (validEmployeeIds.length > 1) {
            matchStage.employeeId = {
                $in: validEmployeeIds.map(id => new mongoose.Types.ObjectId(id)),
            };
        }

        const customerIdArray = typeof customerId === "string"
            ? customerId.split(",")
            : Array.isArray(customerId) ? customerId : [];

        const validCustomerIds = customerIdArray.filter(id =>
            mongoose.Types.ObjectId.isValid(id)
        );

        if (validCustomerIds.length === 1) {
            matchStage.customerId = new mongoose.Types.ObjectId(validCustomerIds[0]);
        } else if (validCustomerIds.length > 1) {
            matchStage.customerId = {
                $in: validCustomerIds.map(id => new mongoose.Types.ObjectId(id)),
            };
        }


        if (status === "Sales by Product") {
            const pipeline = [
                { $match: matchStage },
                { $unwind: "$product" },

                // Step 1: Add discountedPrice and discountQuantity fields
                {
                    $addFields: {
                        "product.discountedPrice": {
                            $cond: [
                                {
                                    $and: [
                                        { $ifNull: ["$product.selectedDiscount", null] },
                                        { $ifNull: ["$product.selectedDiscount.price", null] }
                                    ]
                                },
                                "$product.selectedDiscount.price",
                                "$product.price"
                            ]
                        },
                        "product.discountQuantity": {
                            $cond: [
                                {
                                    $and: [
                                        { $ifNull: ["$product.selectedDiscount", null] },
                                        { $ifNull: ["$product.quantity", null] },
                                        { $gt: ["$product.quantity", 0] }
                                    ]
                                },
                                "$product.quantity",
                                0
                            ]
                        }
                    }
                },

                // Step 2: Add discountAmountPerItem = original - discounted, only if discount exists
                {
                    $addFields: {
                        "product.discountAmountPerItem": {
                            $cond: [
                                {
                                    $and: [
                                        { $ifNull: ["$product.selectedDiscount", null] },
                                        { $ifNull: ["$product.selectedDiscount.price", null] },
                                        { $gt: ["$product.discountQuantity", 0] }
                                    ]
                                },
                                {
                                    $subtract: [
                                        { $ifNull: ["$product.price", 0] },
                                        { $ifNull: ["$product.discountedPrice", 0] }
                                    ]
                                },
                                0
                            ]
                        }
                    }
                },

                // Step 3: Grouping and calculating total discount
                {
                    $group: {
                        _id: "$product.name",
                        totalQty: { $sum: "$product.quantity" },
                        totalRevenue: {
                            $sum: {
                                $multiply: [
                                    { $ifNull: ["$product.quantity", 0] },
                                    { $ifNull: ["$product.discountedPrice", 0] }
                                ]
                            }
                        },
                        totalDiscount: {
                            $sum: {
                                $cond: [
                                    { $gt: ["$product.discountQuantity", 0] },
                                    {
                                        $multiply: [
                                            { $ifNull: ["$product.discountQuantity", 0] },
                                            { $ifNull: ["$product.selectedDiscount.price", 0] }
                                        ]
                                    },
                                    0
                                ]
                            }
                        },
                        debugDiscount: {
                            $push: {
                                quantity: "$product.quantity",
                                discountQuantity: "$product.discountQuantity",
                                discountAmountPerItem: "$product.selectedDiscount.price",
                                selectedDiscount: "$product.selectedDiscount"
                            }
                        }
                    }
                },

                // Step 4: Final shape of the result
                {
                    $project: {
                        _id: 0,
                        product: "$_id",
                        quantity: "$totalQty",
                        revenue: { $round: ["$totalRevenue", 2] },
                        discount: { $round: ["$totalDiscount", 2] },
                        totalRevenue: {
                            $round: [
                                { $add: ["$totalRevenue", "$totalDiscount"] },
                                2
                            ]
                        }
                        // debugDiscount: 1 // Include for debugging, remove in production
                    }
                },
                { $sort: { revenue: -1 } }
            ];



            const result = await orderitem.aggregate(pipeline);



            // Calculate totals across all products
            const totals = result.reduce(
                (acc, item) => ({
                    totalQuantity: acc.totalQuantity + item.quantity,
                    totalRevenue: acc.totalRevenue + item.revenue,
                    totalDiscount: acc.totalDiscount + item.discount,
                    totalTotalRevenue: acc.totalTotalRevenue + item.totalRevenue
                }),
                {
                    totalQuantity: 0,
                    totalRevenue: 0,
                    totalDiscount: 0,
                    totalTotalRevenue: 0
                }
            );

            // Round totals to 2 decimal places
            totals.totalRevenue = Number(totals.totalRevenue.toFixed(2));
            totals.totalDiscount = Number(totals.totalDiscount.toFixed(2));
            totals.totalTotalRevenue = Number(totals.totalTotalRevenue.toFixed(2));

            return res.send({
                salesByProduct: result || [],
                totals
            });
        }

        if (status === "Tax Summary") {
            const pipeline = [
                { $match: matchStage },
                { $unwind: "$tax" },
                {
                    $group: {
                        _id: "$tax.name",
                        totalTax: { $sum: { $ifNull: ["$tax.addtax", 0] } },
                        taxCount: { $sum: 1 }
                    }
                },
                {
                    $project: {
                        _id: 0,
                        taxName: "$_id",
                        totalTax: { $round: ["$totalTax", 2] },
                        taxCount: 1
                    }
                },
                { $sort: { totalTax: -1 } }
            ];

            const result = await orderitem.aggregate(pipeline);

            // Totals
            const totals = result.reduce(
                (acc, item) => ({
                    totalTax: acc.totalTax + item.totalTax,
                    totalCount: acc.totalCount + item.taxCount
                }),
                { totalTax: 0, totalCount: 0 }
            );

            totals.totalTax = Number(totals.totalTax.toFixed(2));

            return res.send({
                taxSummary: result || [],
                totals
            });
        }

        if (status === "Net vs Gross Sales") {
            const pipeline = [
                { $match: matchStage },
                {
                    $group: {
                        _id: null,
                        grossSales: { $sum: { $add: ["$grandTotal", { $ifNull: ["$discountAmount", 0] }] } },
                        netSales: { $sum: "$grandTotal" },
                        totalTax: { $sum: { $ifNull: ["$lineValueTax", 0] } },
                        totalDiscount: { $sum: { $ifNull: ["$discountAmount", 0] } },
                        orderCount: { $sum: 1 }
                    }
                },
                {
                    $project: {
                        _id: 0,
                        grossSales: { $round: ["$grossSales", 2] },
                        netSales: { $round: ["$netSales", 2] },
                        totalTax: { $round: ["$totalTax", 2] }, // fixed this line
                        totalDiscount: { $round: ["$totalDiscount", 2] },
                        orderCount: 1
                    }
                }
            ];

            const [result] = await orderitem.aggregate(pipeline);

            return res.send({
                netVsGrossSales: result || {
                    grossSales: 0,
                    netSales: 0,
                    totalTax: 0,
                    totalDiscount: 0,
                    orderCount: 0
                }
            });
        }

        if (status === "Payment Type Summary") {
            const pipeline = [
                { $match: matchStage },

                // Step 1: Replace null paymentType with "cash"
                {
                    $addFields: {
                        paymentTypeFixed: { $ifNull: ["$paymentType", "cash"] }
                    }
                },

                // Step 2: Group to get totals by paymentTypeFixed
                {
                    $group: {
                        _id: "$paymentTypeFixed",
                        totalAmount: { $sum: "$grandTotal" },
                        orderCount: { $sum: 1 }
                    }
                },

                // Step 3: Lookup payment details from "paymentlists"
                {
                    $lookup: {
                        from: "paymentlists",
                        localField: "_id",
                        foreignField: "_id",
                        as: "paymentDetails"
                    }
                },

                // Step 4: Normalize names and IDs (group "Cash"/null together)
                {
                    $addFields: {
                        paymentTypeName: {
                            $cond: [
                                {
                                    $or: [
                                        { $eq: ["$_id", "cash"] },
                                        { $eq: [{ $arrayElemAt: ["$paymentDetails.name", 0] }, "Cash"] }
                                    ]
                                },
                                "Cash",
                                { $arrayElemAt: ["$paymentDetails.name", 0] }
                            ]
                        },
                        normalizedKey: {
                            $cond: [
                                {
                                    $or: [
                                        { $eq: ["$_id", "cash"] },
                                        { $eq: [{ $arrayElemAt: ["$paymentDetails.name", 0] }, "Cash"] }
                                    ]
                                },
                                "cash",
                                "$_id"
                            ]
                        }
                    }
                },

                // Step 5: Re-group based on normalized key
                {
                    $group: {
                        _id: "$normalizedKey",
                        paymentTypeName: { $first: "$paymentTypeName" },
                        totalAmount: { $sum: "$totalAmount" },
                        orderCount: { $sum: "$orderCount" }
                    }
                },

                // Step 6: Combine totals and calculate percentages
                {
                    $group: {
                        _id: null,
                        paymentTypes: {
                            $push: {
                                paymentTypeName: "$paymentTypeName",
                                totalAmount: "$totalAmount",
                                orderCount: "$orderCount"
                            }
                        },
                        totalAllAmount: { $sum: "$totalAmount" },
                        totalAllOrders: { $sum: "$orderCount" }
                    }
                },

                { $unwind: "$paymentTypes" },

                // Step 7: Calculate percentage share
                {
                    $addFields: {
                        "paymentTypes.percentage": {
                            $round: [
                                {
                                    $cond: [
                                        { $gt: ["$totalAllAmount", 0] },
                                        {
                                            $multiply: [
                                                { $divide: ["$paymentTypes.totalAmount", "$totalAllAmount"] },
                                                100
                                            ]
                                        },
                                        0
                                    ]
                                },
                                2
                            ]
                        }
                    }
                },

                // Step 8: Final regroup
                {
                    $group: {
                        _id: null,
                        paymentTypes: { $push: "$paymentTypes" },
                        totalAllAmount: { $first: "$totalAllAmount" },
                        totalAllOrders: { $first: "$totalAllOrders" }
                    }
                },

                // Step 9: Add 100% total marker
                {
                    $addFields: {
                        totalPercentage: 100
                    }
                },

                // Step 10: Final projection
                {
                    $project: {
                        _id: 0,
                        paymentTypes: 1,
                        totalAllAmount: { $round: ["$totalAllAmount", 2] },
                        totalAllOrders: 1,
                        totalPercentage: 1
                    }
                }
            ];
            const [result] = await orderitem.aggregate(pipeline);

            return res.send({
                paymentTypeSummary: result || {
                    paymentTypes: [],
                    totalAllAmount: 0,
                    totalAllOrders: 0
                }
            });
        }

        return res.status(400).send("Invalid status");
    } catch (error) {
        console.error(error);
        res.status(500).send("Internal Server Error");
    }
};

export const getAllStripeTransactions = async (req, res) => {
    const { accountId } = req.params;
    const { startDate, endDate } = req.query;

    const startTime = startDate ? Math.floor(new Date(startDate).getTime() / 1000) : undefined;
    const endTime = endDate ? Math.floor(new Date(endDate).getTime() / 1000) : undefined;

    try {
        let allTransactions = [];
        let hasMore = true;
        let startingAfter = null;

        while (hasMore) {
            const params = {
                limit: 100,
                ...(startTime && endTime && { created: { gte: startTime, lte: endTime } }),
                ...(startTime && !endTime && { created: { gte: startTime } }),
                ...(!startTime && endTime && { created: { lte: endTime } }),
                ...(startingAfter && { starting_after: startingAfter }),
            };

            const result = await stripe.balanceTransactions.list(params, {
                stripeAccount: accountId,
            });

            for (const txn of result.data) {
                let cardBrand = null;
                let paymentMethodType = null;
                if (txn.source && txn.type === 'charge') {
                    try {
                        const charge = await stripe.charges.retrieve(txn.source, {
                            stripeAccount: accountId,
                        });

                        if (charge.payment_method_details) {
                            paymentMethodType = charge.payment_method_details.type;

                            if (paymentMethodType === 'card') {
                                cardBrand = charge.payment_method_details.card?.brand || null;
                            }
                        }
                    } catch (err) {
                        console.warn(`Failed to get charge details for ${txn.source}: ${err.message}`);
                    }
                }

                allTransactions.push({
                    ...txn,
                    paymentMethod: paymentMethodType,
                    cardBrand: cardBrand,
                });
            }

            hasMore = result.has_more;
            if (hasMore) {
                startingAfter = result.data[result.data.length - 1].id;
            }
        }

        res.status(200).json({
            total: allTransactions.length,
            transactions: allTransactions,
        });
    } catch (error) {
        console.error('Error fetching Stripe transactions:', error.message);
        res.status(500).json({ error: 'Failed to fetch transactions' });
    }
};