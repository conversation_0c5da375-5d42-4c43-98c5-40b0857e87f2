import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { assets } from "../assets/assets";
import { Link } from "react-router-dom";
import {
    fetchOnlineOrders,
    selectOrders,
    selectLoading,
    selectError,
    type OnlineOrderItem
} from "../redux-store/slices/subOnlineOrderitemSlice";
import {
    fetchParentOnlineOrdersByCustomerId,
    selectCustomerOrders,
    selectParentOnlineOrderLoading,
    selectParentOnlineOrderError
} from "../redux-store/slices/parentOnlineOrderSlice";
import { selectCurrentCustomer } from "../redux-store/slices/customerSlice";

interface Order {
    id: string;
    restaurant: string;
    status: "active" | "delivered";
    date: string;
    items: Array<{
        name: string;
        quantity: number;
        image?: string | null; // Add optional image field
    }>;
    totalPrice: number;
    orderNumber: string;
}

export default function OrderHistoryPage() {
    const dispatch = useDispatch();

    // Get current user
    const currentCustomer = useSelector(selectCurrentCustomer);
    const userId = currentCustomer?._id;
    // Sub orders (order items)
    const reduxOrders = useSelector(selectOrders);
    const subOrderLoading = useSelector(selectLoading);
    const subOrderError = useSelector(selectError);

    // Parent orders (main orders)
    const parentOrders = useSelector(selectCustomerOrders);
    const parentOrderLoading = useSelector(selectParentOnlineOrderLoading);
    const parentOrderError = useSelector(selectParentOnlineOrderError);

    const [orders, setOrders] = useState<Order[]>([]);

    // Combined loading and error states
    const loading = subOrderLoading || parentOrderLoading;
    const error = subOrderError || parentOrderError;

    // Transform Redux orders to match the UI interface
    const transformReduxOrderToUIOrder = (reduxOrder: OnlineOrderItem): Order => {
        // Extract product names, quantities, and images from the product array
        const items = reduxOrder.product?.map(product => ({
            name: product.name || product.title || "Unknown Item",
            quantity: product.quantity || 1,
            image: product.image || null // Add product image
        })) || [{ name: "No items", quantity: 0, image: null }];

        // Calculate correct total price based on products
        let calculatedTotal = 0;
        if (reduxOrder.product && reduxOrder.product.length > 0) {
            calculatedTotal = reduxOrder.product.reduce((total, product) => {
                const price = product.price || product.discountPrice || 0;
                const quantity = product.quantity || 1;
                const modifierPrice = product.modifierPrice || 0;
                return total + ((price + modifierPrice) * quantity);
            }, 0);
        }

        // Use calculated total if available, otherwise fall back to Amount
        const totalPrice = calculatedTotal > 0 ? calculatedTotal : (reduxOrder.Amount || 0);

        // Determine status based on orderStatus
        const status: "active" | "delivered" =
            reduxOrder.orderStatus === "done" ? "delivered" : "active";

        // Format date
        const date = reduxOrder.createdAt
            ? new Date(reduxOrder.createdAt).toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            })
            : "Unknown date";

        // Get restaurant name from the product's restaurant data or fallback
        let restaurantName = "Restaurant";
        if (reduxOrder.product && reduxOrder.product.length > 0 && reduxOrder.product[0].restaurant) {
            restaurantName = reduxOrder.product[0].restaurant.name || "Restaurant";
        }
        console.log("redux order", reduxOrder);
        console.log('Order calculation debug:', {
            orderId: reduxOrder._id,
            products: reduxOrder.product,
            restaurantName: restaurantName,
            calculatedTotal,
            originalAmount: reduxOrder.Amount,
            finalTotal: totalPrice
        });

        return {
            id: reduxOrder._id || Math.random().toString(),
            restaurant: restaurantName,
            status,
            date,
            items,
            totalPrice: totalPrice,
            orderNumber: reduxOrder.OrderNumber || reduxOrder.orderNo?.toString() || "N/A"
        };
    };

    // Transform parent orders to match the UI interface (new API structure)
    const transformParentOrderToUIOrder = (parentOrder: any): Order => {
        // Extract items from subOnlineOrderId array
        const items = parentOrder.subOnlineOrderId?.flatMap((subOrder: any) =>
            subOrder.product?.map((product: any) => ({
                name: product.name || product.title || "Unknown Item",
                quantity: product.quantity || 1,
                image: product.image || null // Add product image
            })) || []
        ) || [{ name: "Order items", quantity: 1, image: null }];

        // Use totalAmount from parent order directly (this is the final amount including taxes, fees, etc.)
        const totalPrice = parentOrder.totalAmount || 0;

        // Determine status from sub-orders
        const status: "active" | "delivered" =
            parentOrder.subOnlineOrderId?.some((subOrder: any) => subOrder.orderStatus === "done") ? "delivered" : "active";

        // Format date - use the first sub-order's date or parent order date
        const orderDate = parentOrder.subOnlineOrderId?.[0]?.createdAt || parentOrder.createdAt;
        const date = orderDate
            ? new Date(orderDate).toLocaleDateString('en-US', {
                weekday: 'short',
                day: 'numeric',
                month: 'short',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            })
            : "Unknown date";

        // Get restaurant name from the first product's restaurant data
        let restaurantName = "Restaurant";
        if (parentOrder.subOnlineOrderId?.[0]?.product?.[0]?.restaurant?.name) {
            restaurantName = parentOrder.subOnlineOrderId[0].product[0].restaurant.name;
        }

        console.log('Parent order calculation debug:', {
            orderId: parentOrder._id,
            subOnlineOrderId: parentOrder.subOnlineOrderId,
            restaurantName,
            totalAmount: parentOrder.totalAmount,
            finalTotal: totalPrice
        });

        return {
            id: parentOrder._id || Math.random().toString(),
            restaurant: restaurantName,
            status,
            date,
            items,
            totalPrice: totalPrice,
            orderNumber: parentOrder.OrderNo || parentOrder._id?.slice(-6) || "N/A"
        };
    };

    useEffect(() => {
        // Only fetch orders if we have a user ID
        if (userId) {
           

            // Fetch both sub orders and parent orders
            dispatch(fetchOnlineOrders({ userId }) as any);
            dispatch(fetchParentOnlineOrdersByCustomerId(userId) as any);
        } else {
            console.log('No user ID available for fetching orders');
        }
    }, [dispatch, userId, currentCustomer]);

    useEffect(() => {
        // Transform both sub orders and parent orders to UI format
        const allOrders: Order[] = [];

        console.log('=== API RESPONSE DEBUG ===');


        // Add transformed sub orders
        if (reduxOrders && reduxOrders.length > 0) {
            console.log('Processing', reduxOrders.length, 'sub orders');
            const transformedSubOrders = reduxOrders.map(transformReduxOrderToUIOrder);
            allOrders.push(...transformedSubOrders);
        }

        // Add transformed parent orders
        if (parentOrders && parentOrders.length > 0) {
            console.log('Processing', parentOrders.length, 'parent orders');
            const transformedParentOrders = parentOrders.map(transformParentOrderToUIOrder);
            allOrders.push(...transformedParentOrders);
        }

        // Sort orders by date (newest first)
        allOrders.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

        setOrders(allOrders);
        console.log('Final transformed orders for user', userId, ':', allOrders);
        console.log('Total orders found:', allOrders.length);
        console.log("order history", allOrders);
    }, [reduxOrders, parentOrders, userId]);

    const activeOrders = orders.filter(order => order.status === "active");
    const pastOrders = orders.filter(order => order.status === "delivered");

    useEffect(() => {
      console.log('activeOrders',activeOrders)
    }, [activeOrders]);

    // Show message if user is not logged in
    if (!currentCustomer || !userId) {
        return (
            <div className="block min-h-screen bg-gray-50 w-full">
                <div className="max-w-2xl mx-auto p-4 w-full">
                    <div className="text-center py-12">
                        <h2 className="text-xl font-bold text-gray-800 mb-4">Please Log In</h2>
                        <p className="text-gray-600 mb-6">You need to be logged in to view your order history.</p>
                        <Link to="/login" className="bg-primary text-white px-6 py-3 rounded-full font-medium hover:bg-orange-600 transition-colors">
                            Go to Login
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    // Show loading state
      if (loading) {
        return (
            <div className="text-center py-12 h-screen flex flex-col justify-center items-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
                <p className="mt-4 text-gray-600">Loading orders...</p>
            </div>
        );
    }

    // Show error state
    if (error) {
        return (
            <div className="block min-h-screen bg-gray-50 w-full">
                <div className="max-w-2xl mx-auto p-4 w-full">
                    <div className="flex justify-center items-center h-64">
                        <div className="text-lg text-red-500">Error: {error}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="block min-h-screen bg-gray-50 w-full">
            <div className="max-w-2xl mx-auto p-4 w-full">
                <h1 className="text-2xl font-bold mb-8">Order History</h1>
                <div className="space-y-6 mb-8">
                    {activeOrders.length > 0 ? (
                        activeOrders.map(order => (
                            <OrderCard
                                key={order.id}
                                order={order}
                                link={'/tracking'}
                                buttonText="Track Order in Realtime"
                                buttonColor="bg-primary"
                            />
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No active orders found
                        </div>
                    )}
                </div>

                {/* Past Orders Section */}
                <h2 className="text-base font-bold mb-4">Past Orders</h2>
                <div className="space-y-6">
                    {pastOrders.length > 0 ? (
                        pastOrders.map(order => (
                            <OrderCard
                                key={order.id}
                                link="#"
                                order={order}
                                buttonText="Reorder"
                                buttonColor="bg-primary"
                            />
                        ))
                    ) : (
                        <div className="text-center py-8 text-gray-500">
                            No past orders found
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

interface OrderCardProps {
    order: Order;
    buttonText: string;
    buttonColor: string;
    link: string;
}

function OrderCard({ order, buttonText, buttonColor, link }: OrderCardProps) {
    // Get the first product image or fallback to default
    const getOrderImage = () => {
        if (order.items && order.items.length > 0 && order.items[0].image) {
            return order.items[0].image;
        }
        return assets.order_history; // Fallback to default image
    };

    return (
        <div className="flex flex-col sm:flex-row items-start space-y-4 sm:space-y-0 sm:space-x-4 p-2.5 border-gray-200 bg-white rounded-2xl">
            <div className="relative w-full sm:w-auto">
                <img
                    src={getOrderImage()}
                    alt={order.restaurant}
                    className="w-full sm:w-32 h-32 rounded-md object-cover"
                    onError={(e) => {
                        // Fallback to default image if product image fails to load
                        const target = e.target as HTMLImageElement;
                        target.src = assets.order_history;
                    }}
                />
                {order.status === 'delivered' && (
                    <button className="absolute bottom-2 right-2 bg-white rounded-full p-2 shadow-md">
                        <PlusIcon />
                    </button>
                )}
            </div>

            <div className="flex-1 w-full">
                <div className="flex flex-col sm:flex-row justify-between items-start">
                    <div>
                        <h3 className="text-xl font-semibold">{order.restaurant}</h3>
                        <p className="text-gray-500 text-sm">
                            {order.status === "active" ? "Picked up on " : "Delivered on "}
                            {order.date}
                        </p>
                        <p className="text-gray-500 text-sm">Order #{order.orderNumber}</p>
                    </div>
                    <span className="font-bold mt-2 sm:mt-0">$ {order.totalPrice.toFixed(2)}</span>
                </div>

                <div className="mt-2">
                    {order.items.map((item, index) => (
                        <div key={index} className="flex items-center space-x-2 mb-1">
                            {item.image && (
                                <img
                                    src={item.image}
                                    alt={item.name}
                                    className="w-6 h-6 rounded object-cover"
                                    onError={(e) => {
                                        // Hide image if it fails to load
                                        const target = e.target as HTMLImageElement;
                                        target.style.display = 'none';
                                    }}
                                />
                            )}
                            <p className="text-gray-800">
                                {item.quantity}x {item.name}
                            </p>
                        </div>
                    ))}
                </div>

                <div className="mt-4 flex justify-end">
                    <Link to={link} className={`${buttonColor} text-white px-4 py-2 justify-center flex rounded-full hover:opacity-90 w-full sm:w-auto`}>
                        {buttonText}
                    </Link>
                </div>
            </div>
        </div>
    );
}

function PlusIcon() {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="text-primary"
        >
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
        </svg>
    );
}