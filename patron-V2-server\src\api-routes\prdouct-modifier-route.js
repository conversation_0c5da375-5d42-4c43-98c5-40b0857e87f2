import express from 'express'
import {
    getModifiers,
    addModifiers,
    putModifiers,
    getModifiersById,
    updateModifiers,
    deleteModifiers
} from '../api/product-modifier.js'
const router = express.Router()
router.get('/modifiers', getModifiers)
router.get('/modifiers/:_id', getModifiersById)
router.post('/modifiers', addModifiers)
router.put('/modifiers/:id', putModifiers)
router.put('/modifier/:id', updateModifiers)
router.delete('/modifiers/:id', deleteModifiers)

export default router;