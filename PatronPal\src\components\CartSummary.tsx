import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { ChevronUp, ChevronDown } from 'lucide-react';
import {
    selectCartItems,
    selectCartTotal,
    selectCartSubtotal,
    selectCartTaxAmount,
    selectCartTaxPercentage,
    type CartItem
} from '../redux-store/slices/cartSlice';
// import type { RootState } from '../redux-store/store';

interface CartSummaryProps {
    className?: string;
}

interface GroupedCartItems {
    [restaurantId: string]: {
        restaurantName: string;
        restaurantImage?: string;
        items: CartItem[];
    };
}

const CartSummary: React.FC<CartSummaryProps> = ({ className = '' }) => {
    const cartItems = useSelector(selectCartItems);
    const totalPrice = useSelector(selectCartTotal);
    const subtotalPrice = useSelector(selectCartSubtotal);
    const taxAmount = useSelector(selectCartTaxAmount);
    const taxPercentage = useSelector(selectCartTaxPercentage);
    const [openItems, setOpenItems] = useState<Record<string, boolean>>({});

    // Always call useEffect hook before any conditional returns
    useEffect(() => {
        console.log('Cart items updated:', cartItems);
    }, [cartItems]);

    // Group items by restaurant
    const groupedItems: GroupedCartItems = cartItems.reduce((acc, item) => {
        const restaurantId = item.restaurantId || 'unknown';
        const restaurantName = item.restaurant?.name || 'Unknown Restaurant';
        const restaurantImage = item.restaurant?.image;

        if (!acc[restaurantId]) {
            acc[restaurantId] = {
                restaurantName,
                restaurantImage,
                items: []
            };
        }

        acc[restaurantId].items.push(item);
        return acc;
    }, {} as GroupedCartItems);

    // Calculate discounts (you can modify these based on your business logic)
    const subtotal = subtotalPrice; // Use actual subtotal from Redux (before tax)
    const couponDiscount = 0; // This should come from your coupon logic
    const pointsDiscount = 0; // This should come from your points logic
    const finalTotal = totalPrice - couponDiscount - pointsDiscount; // Use total price (with tax) for final total

    const calculateItemTotal = (item: CartItem): number => {
        const basePrice = item.discountPrice || item.price;
        const modifierPrice = item.modifierPrice || 0;
        return (basePrice + modifierPrice) * item.quantity;
    };

    const toggleItem = (itemId: string) => {
        setOpenItems((prev) => ({
            ...prev,
            [itemId]: !prev[itemId],
        }));
    };

    const renderModifiers = (item: CartItem) => {
        if (!item.modifiers) return null;

        return (
            <div className=" bg-gray-200 p-2 mt-2 space-y-1">
                {Object.entries(item.modifiers).map(([key, modifier]: [string, any]) => {
                    if (!modifier || !modifier.name) return null;

                    return (
                        <div key={key} className="flex justify-between items-center text-sm">
                            <span className="flex-1">{modifier.name}</span>
                            <div className='w-80'>
                                {modifier.price > 0 && (
                                    <span className="">+${modifier.price.toFixed(2)}</span>
                                )}
                            </div>
                        </div>
                    );
                })}

                {item.note && (<div className="text-sm italic flex justify-between">
                    <span className="font-medium">Note:</span>
                    <div className='w-80'>
                        <span className="">{item.note}
                        </span>
                    </div>
                </div>)}

            </div>
        );
    };

    // Conditional return moved after all hooks
    if (cartItems.length === 0) {
        return (
            <div className={`w-80 bg-white border border-gray-200 rounded-lg p-6 text-center ${className}`}>
                <div className="text-gray-500">Your cart is empty</div>
            </div>
        );
    }


    return (
        <>
            <div className={` bg-white rounded-lg  ${className}`}>
                {/* Items grouped by restaurant */}

                {/* Column Headers */}
                <div className="grid grid-cols-12 gap-1 text-sm border-b border-gray-200 text-gray-500 font-medium p-3">
                    <div className="col-span-4">Item Name</div>
                    <div className="col-span-1 text-center">Qty</div>
                    <div className="col-span-2 text-center">Base Price</div>
                    <div className="col-span-2 text-center">Modifiers</div>
                    <div className="col-span-2 text-center">Total</div>
                    <div className="col-span-1"></div>
                </div>
                <div className="space-y-6 mb-6 max-h-96 overflow-y-auto p-3">
                    {Object.entries(groupedItems).map(([restaurantId, group]) => (
                        <div key={restaurantId} className="space-y-3">
                            {/* Restaurant Header */}
                            <div className="flex items-center space-x-3 pb-2 ">
                                {group.restaurantImage && (
                                    <img
                                        src={group.restaurantImage}
                                        alt={group.restaurantName}
                                        className="w-8 h-8 rounded-full object-cover"
                                    />
                                )}
                                <div className="flex-1">
                                    <h3 className="font-medium text-gray-900 text-sm">{group.restaurantName}</h3>
                                    <p className="text-sm text-gray-500">{group.items.length} items</p>
                                </div>
                            </div>


                            {/* Restaurant Items */}
                            {group.items.map((item, index) => {
                                const isOpen = openItems[item._id] || false;

                                return (
                                    <div key={`${item._id}-${index}`} className="space-y-2">
                                        <div className="grid grid-cols-12 gap-1 items-start text-sm">
                                            <div className="col-span-4">
                                                <div className="font-medium text-gray-900 text-sm leading-tight">
                                                    {item.name}
                                                </div>
                                                {item.deal && (
                                                    <div className="text-sm text-green-600 font-medium mt-1">
                                                        {item.deal}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="col-span-1 flex items-center justify-center space-x-1">
                                                <span className="text-center min-w-[20px] text-sm font-medium">
                                                    {item.quantity}
                                                </span>
                                            </div>

                                            <div className="col-span-2 text-center text-sm text-gray-700">
                                                ${(item.discountPrice || item.price).toFixed(2)}
                                            </div>

                                            <div className="col-span-2 text-center text-sm text-gray-700">
                                                {item.modifierPrice && (
                                                    <div className="text-sm text-gray-500">
                                                        +${item.modifierPrice.toFixed(2)}
                                                    </div>
                                                )}
                                            </div>

                                            <div className="col-span-2 text-center font-medium text-gray-900 text-sm">
                                                ${calculateItemTotal(item).toFixed(2)}
                                            </div>

                                            <div className="col-span-1 text-center font-normal">
                                                {item.modifiers && Object.keys(item.modifiers).length > 0 &&
                                                    (!isOpen ? (
                                                        <ChevronDown size={22} onClick={() => toggleItem(item._id)} className='cursor-pointer' />
                                                    ) : (
                                                        <ChevronUp size={22} onClick={() => toggleItem(item._id)} className='cursor-pointer' />
                                                    ))}
                                            </div>
                                        </div>

                                        {isOpen && item.modifiers && Object.keys(item.modifiers).length > 0 && (
                                            <div className="mt-2">
                                                {renderModifiers(item)}
                                            </div>
                                        )}
                                    </div>
                                );
                            })}

                        </div>
                    ))}
                </div>
            </div>
            {/* Summary Section */}
            <div className="p-3 space-y-3 bg-white rounded-lg ">
                <div className="flex justify-between text-sm font-medium text-gray-900">
                    <span className=' text-gray-600'>SUBTOTAL</span>
                    <span>${subtotal.toFixed(2)}</span>
                </div>


                <div className="flex justify-between text-sm text-gray-900">
                    <span className=' text-gray-600'>Coupon Discount</span>
                    <span>-${couponDiscount.toFixed(2)}</span>
                </div>


                <div className="flex justify-between text-sm text-gray-900">
                    <span className=' text-gray-600'>Points Discount</span>
                    <span>-${pointsDiscount.toFixed(2)}</span>
                </div>

                {/* Tax Information - Only show if tax percentage > 0 */}
                {taxPercentage > 0 && (
                    <div className="flex justify-between text-sm text-gray-900">
                        <span className=' text-gray-600'>Tax ({taxPercentage.toFixed(1)}%)</span>
                        <span>${taxAmount.toFixed(2)}</span>
                    </div>
                )}

                <div className="flex justify-between text-sm font-medium text-gray-900 pt-2 ">
                    <span className=' text-gray-600'>Total</span>
                    <span className="text-orange-500 font-semibold">${finalTotal.toFixed(2)}</span>
                </div>

                <button className="w-full text-blue-500 text-start text-sm font-medium hover:text-blue-600 transition-colors">
                    See Summary
                </button>
            </div>
        </>
    );
};

export default CartSummary;