import apiClient from '../redux-store/config';

export interface RestaurantData {
  _id: string;
  name: string;
  userId: {
    _id: string;
    firstname: string;
    lastname: string;
    email: string;
    stripe_account_id?: string;
    stripe_acess_token?: string;
    stripe_refresh_token?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * Gets the current restaurant data including stripe account information
 * @param userId - The restaurant's userId from localStorage
 * @returns Promise<RestaurantData | null> - Restaurant data with stripe info
 */
export const getCurrentRestaurantData = async (userId: string): Promise<RestaurantData | null> => {
  try {
    if (!userId) {
      console.error('No userId provided to get restaurant data');
      return null;
    }

    // Fetch restaurant data by userId
    const response = await apiClient.get<RestaurantData[]>(`/Pdevice?userId=${userId}`);
    
    if (response.data && response.data.length > 0) {
      const restaurant = response.data[0]; // Get the first restaurant for this userId
      console.log('Restaurant data fetched:', restaurant);
      return restaurant;
    } else {
      console.error('No restaurant found for userId:', userId);
      return null;
    }
  } catch (error: any) {
    console.error('Error fetching restaurant data:', error);
    return null;
  }
};

/**
 * Gets the stripe account ID for the current restaurant
 * @param userId - The restaurant's userId from localStorage
 * @returns Promise<string | null> - Stripe account ID or null if not found
 */
export const getCurrentRestaurantStripeAccount = async (userId: string): Promise<string | null> => {
  try {
    const restaurantData = await getCurrentRestaurantData(userId);
    
    if (restaurantData?.userId?.stripe_account_id) {
      console.log('Stripe account ID found:', restaurantData.userId.stripe_account_id);
      return restaurantData.userId.stripe_account_id;
    } else {
      console.error('No stripe account ID found for restaurant:', userId);
      return null;
    }
  } catch (error: any) {
    console.error('Error getting stripe account ID:', error);
    return null;
  }
};

/**
 * Gets the stored restaurant userId from localStorage
 * @returns string | null - The stored userId or null if not found
 */
export const getStoredRestaurantUserId = (): string | null => {
  return localStorage.getItem('userid');
};

/**
 * Validates if the restaurant has stripe integration set up
 * @param userId - The restaurant's userId
 * @returns Promise<boolean> - True if stripe is set up, false otherwise
 */
export const validateRestaurantStripeSetup = async (userId: string): Promise<boolean> => {
  try {
    const restaurantData = await getCurrentRestaurantData(userId);
    
    return !!(
      restaurantData?.userId?.stripe_account_id &&
      restaurantData?.userId?.stripe_acess_token
    );
  } catch (error: any) {
    console.error('Error validating stripe setup:', error);
    return false;
  }
};
