/* eslint-disable @typescript-eslint/no-explicit-any */
// This file should only contain type declarations and not React component implementations or JSX.
// Move all implementation and JSX code to a .tsx file, e.g., SignupPage.tsx.

// Type definitions
interface GoogleCredentialResponse {
  credential: string;
  select_by?: string;
  client_id?: string;
}

interface AppleSignInResponse {
  authorization: {
    code: string;
    id_token: string;
    state?: string;
  };
  user?: {
    email: string;
    name: {
      firstName: string;
      lastName: string;
    };
  };
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: {
            client_id: string;
            callback: (response: GoogleCredentialResponse) => void;
            auto_select?: boolean;
            cancel_on_tap_outside?: boolean;
          }) => void;
          prompt: (callback?: (notification: any) => void) => void;
          renderButton: (parent: HTMLElement, options: any) => void;
          disableAutoSelect: () => void;
        };
      };
    };
    AppleID?: {
      auth: {
        init: (config: {
          clientId: string;
          scope: string;
          redirectURI: string;
          state?: string;
          usePopup?: boolean;
        }) => void;
        signIn: (config?: {
          usePopup?: boolean;
        }) => Promise<AppleSignInResponse>;
      };
    };
  }
}

interface SignupFormData {
  email: string;
  password: string;
  confirmPassword: string;
  referralCode?: string;
  firstName: string;
  lastName: string;
  phone: string;
  birthDate: string;
}