/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";

// Menu interface
interface Menu {
  _id: string;
  treeData: any;
  superUserId: any;
  role: string;
  playlandid: string;
  __v?: number;
}

// Request interfaces
interface MenuCreateRequest {
  treeData: any;
  superUserId: string;
  role: string;
  playlandid: string;
}

interface MenuUpdateRequest extends MenuCreateRequest {
  _id: string;
  __v?: number;
}

interface MenuFilters {
  superUserId?: string;
  playlandid?: string;
  role?: string;
}

// State interface
interface MenuState {
  menus: Menu[];
  currentMenu: Menu | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: MenuState = {
  menus: [],
  currentMenu: null,
  loading: false,
  error: null,
};

// Async thunks
export const getMenus = createAsyncThunk<
  Menu[],
  MenuFilters,
  { rejectValue: string }
>("menu/getMenus", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.superUserId) params.append("superUserId", filters.superUserId);
    if (filters.playlandid) params.append("playlandid", filters.playlandid);
    if (filters.role) params.append("role", filters.role);

    const response = await apiClient.get<Menu[]>(`/menu?${params.toString()}`);

    console.log("[getMenus] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch menus";
    return rejectWithValue(message);
  }
});

export const getMenuById = createAsyncThunk<
  Menu,
  string,
  { rejectValue: string }
>("menu/getMenuById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Menu>(`/menu/${id}`);
    console.log("[getMenuById] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch menu";
    return rejectWithValue(message);
  }
});

export const createMenu = createAsyncThunk<
  Menu,
  MenuCreateRequest,
  { rejectValue: string }
>("menu/createMenu", async (menuData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<Menu>("/menu", menuData);
    console.log("[createMenu] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to create menu";
    return rejectWithValue(message);
  }
});

export const updateMenu = createAsyncThunk<
  Menu,
  { id: string; menuData: MenuUpdateRequest },
  { rejectValue: string }
>("menu/updateMenu", async ({ id, menuData }, { rejectWithValue }) => {
  try {
    const response = await apiClient.put<{
      data?: Menu;
      message: string;
    }>(`/menu/${id}`, menuData);

    // Handle different response structures
    // If the API returns the updated data directly
    if (response.data.data) {
      return response.data.data;
    }
    // If the API only returns a success message, fetch the updated menu
    else {
      const updatedMenuResponse = await apiClient.get<Menu>(`/menu/${id}`);
      return updatedMenuResponse.data;
    }
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to update menu";
    return rejectWithValue(message);
  }
});

export const deleteMenu = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("menu/deleteMenu", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete<{ message: string }>(`/menu/${id}`);
    console.log("[deleteMenu] Menu deleted successfully");
    return id; // Return the deleted menu ID
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to delete menu";
    return rejectWithValue(message);
  }
});

// Menu slice
const menuSlice = createSlice({
  name: "menu",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentMenu: (state, action: PayloadAction<Menu>) => {
      state.currentMenu = action.payload;
    },
    clearCurrentMenu: (state) => {
      state.currentMenu = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get menus
      .addCase(getMenus.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMenus.fulfilled, (state, action) => {
        state.loading = false;
        state.menus = action.payload;
      })
      .addCase(getMenus.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch menus";
      })

      // Get menu by ID
      .addCase(getMenuById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getMenuById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentMenu = action.payload;
      })
      .addCase(getMenuById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch menu";
      })

      // Create menu
      .addCase(createMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createMenu.fulfilled, (state, action) => {
        state.loading = false;
        state.menus.push(action.payload);
      })
      .addCase(createMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create menu";
      })

      // Update menu
      .addCase(updateMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateMenu.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.menus.findIndex(
          (m) => m._id === action.payload._id
        );
        if (index !== -1) {
          state.menus[index] = action.payload;
        }
        if (state.currentMenu && state.currentMenu._id === action.payload._id) {
          state.currentMenu = action.payload;
        }
      })
      .addCase(updateMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update menu";
      })

      // Delete menu
      .addCase(deleteMenu.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteMenu.fulfilled, (state, action) => {
        state.loading = false;
        state.menus = state.menus.filter((m) => m._id !== action.payload);
        if (state.currentMenu && state.currentMenu._id === action.payload) {
          state.currentMenu = null;
        }
      })
      .addCase(deleteMenu.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete menu";
      });
  },
});

// Export actions
export const { clearError, setCurrentMenu, clearCurrentMenu } =
  menuSlice.actions;

// Selectors
export const selectMenus = (state: { menu: MenuState }) => state.menu.menus;
export const selectCurrentMenu = (state: { menu: MenuState }) =>
  state.menu.currentMenu;
export const selectMenuLoading = (state: { menu: MenuState }) =>
  state.menu.loading;
export const selectMenuError = (state: { menu: MenuState }) => state.menu.error;

// Export reducer
export default menuSlice.reducer;
