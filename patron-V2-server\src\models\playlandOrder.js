import mongoose from 'mongoose';

const { Schema } = mongoose;

const playlandOrderSchema = new Schema({
  active: {
    type: Boolean,
  },
  categoryId: {
    _id: {
      type: String,
    },
    partyname: {
      type: String,
    },
  },
  conditions: {
    type: Boolean,
  },
  creatdateFormat: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  eveningTime: {
    type: String,
    default: '',
  },
  finalizedata: {
    toppings: {
      type: String,
    },
    drink: {
      type: String,
    },
    termsAndConditions: {
      type: Boolean,
    },
    wearWristband: {
      type: Boolean,
    },
    attendWithoutBands: {
      type: <PERSON>ole<PERSON>,
    },
  },
  image: {
    type: String,
  },
  kidsSlot: {
    type: String,
  },
  location: {
    type: String,
  },
  morningTime: {
    type: String,
  },
  newprice: {
    type: Number,
  },
  orderCapicity: {
    type: Number,
    default: 0,
  },
  pakege: {
    _id: {
      type: String
    },
    partyname: {
      type: String,
    },
    party_pic: {
      type: String,
    },
    shortDescription: {
      type: String,
    },
    longDescription: [{
        description: {
          type: String,
        },
      }],
  },
  product: [{
    product: {
      type: String,
    },
    price: {
      type: Number,
    },
    quant: {
      type: Number,
    },
  }],
  roomName: {
    type: String,
  },
  room_pic: {
    type: String,
  },
  selectedDate: {
    type: Date,
  },
  selectedTime: {
    type: String,
  },
  shortDescription: [{
    description: {
      type: String,
    },
  }],
  totalCapacity: {
    type: String,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
  },
});

const PlaylandOrder = mongoose.model('PlaylandOrder', playlandOrderSchema);
export default PlaylandOrder;
