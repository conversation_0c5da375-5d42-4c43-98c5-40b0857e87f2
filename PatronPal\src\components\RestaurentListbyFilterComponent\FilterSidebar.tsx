import React, { useEffect } from 'react';
import { Search, X } from 'lucide-react';

interface CuisineCategory {
    name: string;
    image: string;
    categoryId?: string;
    children?: CuisineCategory[];
}

interface FilterState {
    sort: string;
    offers: string[];
    businessTypes: string[]; // Changed from cuisines to businessTypes
}

interface FilterSidebarProps {
    activeFilters: FilterState;
    businessTypeCategories: CuisineCategory[]; // Changed from cuisineCategories
    isMobile: boolean;
    showFiltersMobile: boolean;
    onSortChange: (value: string) => void;
    onOfferChange: (value: string) => void;
    onBusinessTypeChange: (value: string) => void; // Changed from onCuisineChange
    onToggleFiltersMobile: () => void;
}

const FilterSidebar: React.FC<FilterSidebarProps> = ({
    activeFilters,
    businessTypeCategories, // Changed from cuisineCategories
    isMobile,
    onSortChange,
    onOfferChange,
    onBusinessTypeChange, // Changed from onCuisineChange
    onToggleFiltersMobile,
}) => {

    useEffect(() => {
        console.log('[businessTypeCategories]', businessTypeCategories)
    }, [businessTypeCategories])


    return (
        <div className={`${isMobile ? 'fixed inset-0 z-50 bg-white overflow-y-auto h-full -top-4' : 'w-64 '}  h-full overflow-y-auto px-4 py-4 pb-24 pr-8 mt-2.5`}>
            {isMobile && (
                <div className="flex justify-between items-center mb-4 sticky top-16 bg-white z-10">
                    <div className="text-lg font-semibold">Filters</div>
                    <button onClick={onToggleFiltersMobile} className="p-1">
                        <X className="w-6 h-6" />
                    </button>
                </div>
            )}

            {!isMobile && (
                <div className="text-lg font-semibold mb-4">Filters</div>
            )}

            {/* Sort options */}
            <div className="mb-6 border-b pb-4 border-gray-300">
                <div className="font-medium mb-2 text-gray-400">Sort By</div>
                <div className="space-y-2">
                    <label className="flex items-center">
                        <input
                            type="radio"
                            name="sort"
                            checked={activeFilters.sort === 'relevance'}
                            onChange={() => onSortChange('relevance')}
                            className="mr-2"
                        />
                        Relevance
                    </label>
                    {/* <label className="flex items-center">
                        <input
                            type="radio"
                            name="sort"
                            checked={activeFilters.sort === 'featured'}
                            onChange={() => onSortChange('featured')}
                            className="mr-2"
                        />
                        Featured Restaurants
                    </label> */}
                    <label className="flex items-center">
                        <input
                            type="radio"
                            name="sort"
                            checked={activeFilters.sort === 'bestselling'}
                            onChange={() => onSortChange('bestselling')}
                            className="mr-2"
                        />
                        Best Selling
                    </label>
                </div>
            </div>

            {/* Quick filters */}
           

            {/* Offers */}
            <div className="mb-6">
                <div className="font-medium mb-2 text-gray-400">Offers</div>
                <div className="space-y-2">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={activeFilters.offers.includes('delivery')}
                            onChange={() => onOfferChange('delivery')}
                            className="mr-2"
                        />
                        Free Delivery
                    </label>
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={activeFilters.offers.includes('vouchers')}
                            onChange={() => onOfferChange('vouchers')}
                            className="mr-2"
                        />
                        Accepts Vouchers
                    </label>
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={activeFilters.offers.includes('deals')}
                            onChange={() => onOfferChange('deals')}
                            className="mr-2"
                        />
                        Deals
                    </label>
                </div>
            </div>

            {/* Cuisines */}
            <div className='border-b pb-4 border-gray-300'>
                <div className="font-medium mb-2 text-gray-400">Cuisines</div>
                <div className="relative mb-3">
                    <Search className="absolute left-2 top-2.5 w-5 h-5 text-gray-900" />
                    <input
                        type="text"
                        placeholder="Search Cuisines"
                        className="pl-8 pr-3 py-2 w-full border border-gray-400 rounded-xl text-md"
                    />
                </div>
                <div className="space-y-4">
                    {businessTypeCategories.map(parent => (
                        <div key={parent.categoryId} className="space-y-2">
                            <label className="flex items-center font-medium">
                                <input
                                    type="checkbox"
                                    checked={activeFilters.businessTypes.includes(parent.name.toLowerCase())}
                                    onChange={() => onBusinessTypeChange(parent.name.toLowerCase())}
                                    className="mr-2"
                                />
                                {parent.name}
                            </label>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default FilterSidebar;