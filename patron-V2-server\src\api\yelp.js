
import axios from 'axios';

// Use a full valid Yelp API Key (must be 32+ characters long)
const YELP_API_KEY = '********************************************************************************************************************************'; // Replace with your real key

// Base headers for Yelp API requests
const headers = {
  Authorization: `Bearer ${YELP_API_KEY}`,
  'Content-Type': 'application/json',
};

// Search for businesses
export const getbusinessDteails = async (req, res) => {
  const { term, location } = req.query;

  try {
    const response = await axios.get('https://api.yelp.com/v3/businesses/search', {
      headers,
      params: { term, location },
    });
    res.json(response.data);
  } catch (err) {
    console.error('Yelp search error:', err.response?.data || err.message);
    res.status(500).json({ error: 'Yelp search failed' });
  }
};

// Get reviews for a specific business
export const getreviews = async (req, res) => {
  const businessId = req.params.id;
console.log(`Fetching reviews for business ID: ${businessId}`);
  try {
    const response = await axios.get(`https://api.yelp.com/v3/businesses/${businessId}/reviews`, {
      headers,
    });
    res.json(response.data);
  } catch (err) {
    console.error('Yelp reviews error:', err.response?.data || err.message);
    res.status(500).json({ error: 'Yelp reviews failed' });
  }
};
