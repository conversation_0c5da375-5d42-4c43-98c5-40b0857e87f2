import express from "express";
const router = express.Router();
import {
    createOrder,
    getOrders,
    getOrderById,
    updateOrder,
    deleteOrder,
    getOrdersByUserId
} from "../api/playlandOrder.js";

router.post('/playlandOrder', createOrder);
router.get('/playlandOrder', getOrders);
router.get('/playlandOrder/:id', getOrderById);
router.patch('/playlandOrder/:id', updateOrder);
router.delete('/playlandOrder/:id', deleteOrder);

router.get('/playlandOrders/:userId', getOrdersByUserId);


export default router;
