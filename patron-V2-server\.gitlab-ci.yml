stages:
  - test
  # - test2
  - deploy-prod
  # - deploy-dev

# Config cache
cache:
  paths:
    - node_modules/

# Test job
test:
  # Docker image
  image: node:latest
  # Attach Test job to Test stage
  stage: test
  # Config to use GitLab Shared Runner with Executor Docker
  tags:
    - my-shell-runner
  script:
    - npm install
    # - npx playwright install
    - npm run test
  # Defines the names of branches and tags the job runs for
  only:
    - div-saad
    # Test job
# test2:
#   # Docker image
#   image: node:latest
#   # Attach Test job to Test stage
#   stage: test
#   # Config to use GitLab Shared Runner with Executor Docker
#   tags:
#     - my-dev-runner
#   script:
#     - npm install
#     - npm run t- sudo npx playwright installest
#   # Defines the names of branches and tags the job runs for
#   only:
#     - div-saad


# # Deploy to development server
# deploy-dev:
#   # Attach Deploy job to Deploy stage
#   stage: deploy-dev
#   # Config to use our Runner with Executor Shell
#   variables:
#     DEV_PORT: 4444
#     JWT_SECRET: secret
#     DEV_DB_NAME: patronworksdev
#     DEV_MONGODB_URI: "mongodb+srv://saudkhanbpk:<EMAIL>/patronworksdev?retryWrites=true&w=majority"
#     DEV_BASE_URL: "http://dev.patronworks.net"
#     CLOUD_NAME: djm945kfs
#   tags:
#     - my-dev-runner
#   script:
#     # Only copy changed files to project folder
#     - ls -la  $DEV_PROJECT_DIR
#     - cp -r -u * $DEV_PROJECT_DIR
#     - cd $DEV_PROJECT_DIR
#     - npm install
#     # Restart NodeJS service with forever for npm run dev
#     - forever stop $DEV_PROJECT_DIR/src/index.js || true
#     - NODE_ENV=development forever start /src/index.js
#   only:
#     - master
#     # Only deploy for npm run dev
#     - $CI_COMMIT_BRANCH == "master" && $CI_COMMIT_TAG == null && $CI_COMMIT_MESSAGE =~ /npm run dev/

# Deploy to production server
deploy-prod:
  # Attach Deploy job to Deploy stage
  stage: deploy-prod
  # Config to use our Runner with Executor Shell
  variables:
    PORT: 3333
    NODE_ENV: production
    JWT_SECRET: secret
    DB_NAME: patronworks
    MONGODB_URI: "mongodb+srv://aamir:<EMAIL>/patronworks?retryWrites=true&w=majority"
    OPENAI_API_KEY: "***************************************************"
    TWILIO_ACCOUNT_ID: "**********************************"
    TWILIO_AUTH_KEY: "56739814f7595abc7354818a8355dafe"
    BASE_URL: "http://www.patronworks.net"
    CLOUD_NAME: djm945kfs
    PROD_LINK1: "https://patronworks.com"
    PROD_LINK2: "https://www.patronworks.net"
  tags:
    - my-shell-runner
  script:
    # Only copy changed files to project folder
    - cp -r -u * $PROJECT_DIR
    - cd $PROJECT_DIR
    - npm install
    # - npx playwright install
    # Restart NodeJS service with forever for npm start
    - forever stop $PROJECT_DIR/src/index.js || true
    - NODE_ENV=production forever start src/index.js
  only:
    - main
    # Only deploy for npm start
    - $CI_COMMIT_BRANCH == "main" && $CI_COMMIT_TAG == null && $CI_COMMIT_MESSAGE =~ /npm start/
