import mongoose from 'mongoose'
var current = new Date();
const timeStamp = new Date(Date.UTC(current.getFullYear(),
    current.getMonth(), current.getDate(), current.getHours(),
    current.getMinutes(), current.getSeconds(), current.getMilliseconds()));
const customerSchema = new mongoose.Schema({
    CustomerId: {
        type: String
    },
    userId:{
        type:mongoose.Schema.Types.ObjectId,
        ref:'user'
    },
    FirstName: {
        type: String
    },
    LastName: {
        type: String
    },
    facebookId: {
        type: String,
        unique: true,
      },

      profile_pic:{
        type:String
      },
    Phone: {
        type: String
    },
    Address: {
        type: String
    },
    City: {
        type: String
    },
    State: {
        type: String
    },
    Email: {
        type: String
    },
    Membership: {
        type: String
    },
    Password:{
        type:String
    },
    ConfirmPassword:{
        type:String
    },
    isActive:{
        type:Boolean
    },
    CustomerLoyalty:{
        
    CardNo: {
        type: String,
    },
    Type: {
        type: String,
        enum:["client","employee"]
    },
    StartDate: {
        type: String,
    },
    ExpiresIn: {
        type: String,  
    },
    creditLimits:{
        type:Number
    },
    ActivateCard:{
        type:Boolean
    },
    Points:{
      type:Number
    },
    Visits:{
        type:Number
    },
    LastVisit:{
         type:Date,
         default:timeStamp
    },
    Purchases:{
      type:Number
    },
    SpentAmount:{
        type:Number
    },
    Gender: {
        type: String
    },
    // History: [{
    //     type: String
    // }],
    Notes: {
        type: String
    },
    timeStamp: {
        type: Date,
        default: timeStamp
    },
      
}
})

const customer = mongoose.model('customer', customerSchema);
export default customer;