import type { Key } from 'react';

export interface Restaurant {
  id: Key | null | undefined;
  _id: string;
  name: string;
  businessType?: string;
  image?: string;
  Line1: string;
  Line2?: string;
  City: string;
  State: string;
  Phoneno: string;
  PostalCode: string;
  Country: string;
  active: boolean | string;
  userId: any;
  delivery?: boolean | string;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  reviews?: Array<{
    _id?: string;
    rating?: number; // Rating field from email reviews
    testimonial: string;
    customerId: any;
    createdDate?: string;
    // Legacy fields (will be ignored in calculations)
    food?: number;
    service?: number;
    ambiance?: number;
    averageScore?: number;
  }>;
  favorites?: Array<{
    _id?: string;
    customerId: any;
  }>;
}

export interface CuisineCategory {
  name: string;
  image: string;
  categoryId?: string;
  children?: CuisineCategory[];
}