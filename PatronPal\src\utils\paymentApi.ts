import apiClient from '../redux-store/config';

export interface PaymentIntentRequest {
  amount: number;
  currency: string;
  stripeAccount: string;
  application_fee_amount: number;
  description: string;
}

export interface PaymentIntentResponse {
  paymentIntent: {
    id: string;
    client_secret: string;
    status: string;
  };
}

export interface CapturePaymentRequest {
  payment_intent_id: string;
}

export interface CapturePaymentResponse {
  id: string;
  status: string;
  charges?: {
    data: Array<{
      id: string;
      amount: number;
      status: string;
    }>;
  };
}

export const createPaymentIntent = async (data: PaymentIntentRequest): Promise<PaymentIntentResponse> => {
  try {
    const response = await apiClient.post<PaymentIntentResponse>('/paymentIntent', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating payment intent:', error);
    throw new Error(error.response?.data?.error || 'Failed to create payment intent');
  }
};

export const capturePayment = async (data: CapturePaymentRequest): Promise<CapturePaymentResponse> => {
  try {
    const response = await apiClient.post<CapturePaymentResponse>('/capture_payment', data);
    return response.data;
  } catch (error: any) {
    console.error('Error capturing payment:', error);
    throw new Error(error.response?.data?.error || 'Failed to capture payment');
  }
};

export const confirmPayment = async (paymentIntentId: string): Promise<any> => {
  try {
    const response = await apiClient.post('/confirm_payment', {
      payment_intent_id: paymentIntentId,
    });
    return response.data;
  } catch (error: any) {
    console.error('Error confirming payment:', error);
    throw new Error(error.response?.data?.error || 'Failed to confirm payment');
  }
};
