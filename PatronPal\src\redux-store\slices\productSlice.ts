/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";

// Product interface
interface Product {
  _id: string;
  name: string;
  price: number;
  totalQuantity: number;
  ProductId: string;
  categoryId: any;
  userId: any;
  isLock: boolean;
  hasPicture: boolean;
  barCode?: string;
  retailPrice?: number;
  active: boolean;
  ingredient?: Array<{
    ingredientId: any;
    quantity?: number;
  }>;
  Product_pic?: string;
  discountPrice?: number;
  shortDescription?: string;
  fullDescription?: string;
  order?: any;
  unit?: any;
  reviewId?: any;
  categoryParents?: any;
  lavel?: number;
  rows?: number;
  cols?: number;
  productPictureId?: string;
  productId?: string;
  productType?: string;
  courseDate?: any;
  value?: number;
  __v?: number;
}

// Request interfaces
interface ProductCreateRequest {
  lavel?: number;
  rows?: number;
  cols?: number;
  categoryParents?: string;
  barCode?: string;
  name: string;
  ingredient?: string; // JSON string
  price: number;
  retailPrice?: number;
  discountPrice?: number;
  shortDescription?: string;
  fullDescription?: string;
  order?: string;
  active: boolean;
  categoryId: string;
  hasPicture: boolean;
  productPictureId?: string;
  totalQuantity: number;
  productId?: string;
  productType?: string;
  userId: string;
  unit?: string;
  Product_pic?: string;
  courseDate?: string; // JSON string
  value?: number;
  isLock?: boolean;
}

interface ProductUpdateRequest extends ProductCreateRequest {
  _id: string;
  __v?: number;
}

export interface ProductFilters {
  categoryId?: string;
  userId?: string;
  key?: string;
}

interface SearchRequest {
  q: string;
}

// State interface
interface ProductState {
  products: Product[];
  currentProduct: Product | null;
  filteredProducts: Product[];
  loading: boolean;
  error: string | null;
  searchResults: any[];
  searchLoading: boolean;
}

// Initial state
const initialState: ProductState = {
  products: [],
  currentProduct: null,
  filteredProducts: [],
  loading: false,
  error: null,
  searchResults: [],
  searchLoading: false,
};

// Async thunks
export const getProducts = createAsyncThunk<
  Product[],
  ProductFilters,
  { rejectValue: string }
>("product/getProducts", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.categoryId) params.append("categoryId", filters.categoryId);
    if (filters.userId) params.append("userId", filters.userId);

    const response = await apiClient.get<Product[]>(
      `/Product?${params.toString()}`
    );

    // console.log("[getProducts] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch products";
    return rejectWithValue(message);
  }
});

export const getProductsPatronpal = createAsyncThunk<
  Product[],
  ProductFilters,
  { rejectValue: string }
>("product/getProductsPatronpal", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.categoryId) params.append("categoryId", filters.categoryId);
    if (filters.userId) params.append("userId", filters.userId);

    const response = await apiClient.get<Product[]>(
      `/Productp?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch products";
    return rejectWithValue(message);
  }
});

export const getProductsWithParentCategory = createAsyncThunk<
  Product[],
  ProductFilters,
  { rejectValue: string }
>(
  "product/getProductsWithParentCategory",
  async (filters, { rejectWithValue }) => {
    try {
      const params = new URLSearchParams();
      if (filters.categoryId) params.append("categoryId", filters.categoryId);
      if (filters.userId) params.append("userId", filters.userId);
      if (filters.key) params.append("type", filters.key); // Add type parameter

      // Add a cache buster to prevent stale data
      params.append("_t", Date.now().toString());

      const response = await apiClient.get<Product[]>(
        `/Products?${params.toString()}`
      );
      
      // Process data immediately to avoid UI delays
      const processedData = response.data || [];
      
      return processedData;
    } catch (error: any) {
      const message = error.response?.data;
      return rejectWithValue(message);
    }
  }
);

export const getFilteredProducts = createAsyncThunk<
  Product[],
  void,
  { rejectValue: string }
>("product/getFilteredProducts", async (_, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Product[]>("/filteredProduct");
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to fetch filtered products";
    return rejectWithValue(message);
  }
});

export const getProductById = createAsyncThunk<
  Product,
  string,
  { rejectValue: string }
>("product/getProductById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Product>(`/Product/${id}`);
    console.log("[getProductById] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch product";
    return rejectWithValue(message);
  }
});

export const getProductsByKey = createAsyncThunk<
  Product[],
  string,
  { rejectValue: string }
>("product/getProductsByKey", async (key, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Product[]>(`/search/${key}`);
    console.log("[getProductsByKey] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to search products";
    return rejectWithValue(message);
  }
});

export const createProduct = createAsyncThunk<
  Product,
  { productData: ProductCreateRequest; file?: File },
  { rejectValue: string }
>(
  "product/createProduct",
  async ({ productData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all product data to formData
      Object.entries(productData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Append file if provided
      if (file) {
        formData.append("Product_pic", file);
      }

      const response = await apiClient.post<Product>("/Product", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to create product";
      return rejectWithValue(message);
    }
  }
);

export const updateProduct = createAsyncThunk<
  Product,
  { id: string; productData: ProductUpdateRequest; file?: File },
  { rejectValue: string }
>(
  "product/updateProduct",
  async ({ id, productData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all product data to formData
      Object.entries(productData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Append file if provided
      if (file) {
        formData.append("Product_pic", file);
      }

      const response = await apiClient.put<{
        result: Product;
        message: string;
      }>(`/Product/${id}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data.result;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to update product";
      return rejectWithValue(message);
    }
  }
);

export const deleteProduct = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("product/deleteProduct", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete<{ message: string }>(`/Product/${id}`);
    return id; // Return the deleted product ID
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to delete product";
    return rejectWithValue(message);
  }
});

export const searchGoogle = createAsyncThunk<
  any[],
  SearchRequest,
  { rejectValue: string }
>("product/searchGoogle", async ({ q }, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<any>(
      `/search?q=${encodeURIComponent(q)}`
    );
    return response.data.items || [];
  } catch (error: any) {
    const message = error.response?.data?.message || "Search failed";
    return rejectWithValue(message);
  }
});

// Product slice
const productSlice = createSlice({
  name: "product",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearProducts: (state) => {
      state.products = [];
      state.loading = false;
      state.error = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
      state.filteredProducts = [];
    },
    setCurrentProduct: (state, action: PayloadAction<Product>) => {
      state.currentProduct = action.payload;
    },
    clearCurrentProduct: (state) => {
      state.currentProduct = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get products
      .addCase(getProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
      })
      .addCase(getProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch products";
      })

      // Get products patronpal
      .addCase(getProductsPatronpal.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductsPatronpal.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
      })
      .addCase(getProductsPatronpal.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch products";
      })

      // Get products with parent category
      .addCase(getProductsWithParentCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductsWithParentCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.products = action.payload;
      })
      .addCase(getProductsWithParentCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch products";
      })

      // Get filtered products
      .addCase(getFilteredProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFilteredProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.filteredProducts = action.payload;
      })
      .addCase(getFilteredProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch filtered products";
      })

      // Get product by ID
      .addCase(getProductById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentProduct = action.payload;
      })
      .addCase(getProductById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch product";
      })

      // Get products by key
      .addCase(getProductsByKey.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getProductsByKey.fulfilled, (state, action) => {
        state.loading = false;
        state.filteredProducts = action.payload;
      })
      .addCase(getProductsByKey.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to search products";
      })

      // Create product
      .addCase(createProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products.push(action.payload);
      })
      .addCase(createProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create product";
      })

      // Update product
      .addCase(updateProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProduct.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.products.findIndex(
          (p) => p._id === action.payload._id
        );
        if (index !== -1) {
          state.products[index] = action.payload;
        }
        if (
          state.currentProduct &&
          state.currentProduct._id === action.payload._id
        ) {
          state.currentProduct = action.payload;
        }
      })
      .addCase(updateProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update product";
      })

      // Delete product
      .addCase(deleteProduct.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteProduct.fulfilled, (state, action) => {
        state.loading = false;
        state.products = state.products.filter((p) => p._id !== action.payload);
        if (
          state.currentProduct &&
          state.currentProduct._id === action.payload
        ) {
          state.currentProduct = null;
        }
      })
      .addCase(deleteProduct.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete product";
      })

      // Google search
      .addCase(searchGoogle.pending, (state) => {
        state.searchLoading = true;
        state.error = null;
      })
      .addCase(searchGoogle.fulfilled, (state, action) => {
        state.searchLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchGoogle.rejected, (state, action) => {
        state.searchLoading = false;
        state.error = action.payload || "Search failed";
      });
  },
});

// Export actions
export const {
  clearError,
  clearProducts,
  clearSearchResults,
  setCurrentProduct,
  clearCurrentProduct,
} = productSlice.actions;

// Selectors
export const selectProducts = (state: { product: ProductState }) =>
  state.product.products;
export const selectCurrentProduct = (state: { product: ProductState }) =>
  state.product.currentProduct;
export const selectFilteredProducts = (state: { product: ProductState }) =>
  state.product.filteredProducts;
export const selectProductLoading = (state: { product: ProductState }) =>
  state.product.loading;
export const selectProductError = (state: { product: ProductState }) =>
  state.product.error;
export const selectSearchResults = (state: { product: ProductState }) =>
  state.product.searchResults;
export const selectSearchLoading = (state: { product: ProductState }) =>
  state.product.searchLoading;

// Export reducer
export default productSlice.reducer;
