import React, { useState } from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import { toast } from 'react-toastify';
import { Mail, Phone, MapPin, Clock } from 'lucide-react';

// Form data interface
interface ContactFormData {
    name: string;
    email: string;
    phone: string;
    subject: string;
    message: string;
}

// Validation schema
const validationSchema = Yup.object({
    name: Yup.string()
        .min(2, 'Name must be at least 2 characters')
        .max(50, 'Name must be less than 50 characters')
        .required('Name is required'),
    email: Yup.string()
        .email('Invalid email address')
        .required('Email is required'),
    phone: Yup.string()
        .matches(/^[+]?[\d\s\-\(\)]+$/, 'Invalid phone number')
        .min(10, 'Phone number must be at least 10 digits')
        .required('Phone number is required'),
    subject: Yup.string()
        .min(5, 'Subject must be at least 5 characters')
        .max(100, 'Subject must be less than 100 characters')
        .required('Subject is required'),
    message: Yup.string()
        .min(10, 'Message must be at least 10 characters')
        .max(500, 'Message must be less than 500 characters')
        .required('Message is required')
});

const ContactUs: React.FC = () => {
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Initial form values
    const initialValues: ContactFormData = {
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: ''
    };

    // Handle form submission
    const handleSubmit = async (values: ContactFormData, { resetForm }: any) => {
        setIsSubmitting(true);
        
        try {
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            console.log('Contact form submitted:', values);
            toast.success('Thank you for your message! We will get back to you soon.');
            resetForm();
        } catch (error) {
            console.error('Error submitting contact form:', error);
            toast.error('Failed to send message. Please try again.');
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="text-center mb-12">
                    <h1 className="text-4xl font-bold text-gray-900 mb-4">
                        Contact <span className="text-orange-500">Us</span>
                    </h1>
                    <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                        Have questions or need support? We'd love to hear from you. 
                        Send us a message and we'll respond as soon as possible.
                    </p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                    {/* Contact Information */}
                    <div className="bg-white rounded-lg shadow-lg p-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6">Get in Touch</h2>
                        
                        <div className="space-y-6">
                            <div className="flex items-start space-x-4">
                                <div className="flex-shrink-0">
                                    <Mail className="w-6 h-6 text-orange-500" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900">Email</h3>
                                    <p className="text-gray-600"><EMAIL></p>
                                    <p className="text-gray-600"><EMAIL></p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-4">
                                <div className="flex-shrink-0">
                                    <Phone className="w-6 h-6 text-orange-500" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900">Phone</h3>
                                    <p className="text-gray-600">+****************</p>
                                    <p className="text-gray-600">+****************</p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-4">
                                <div className="flex-shrink-0">
                                    <MapPin className="w-6 h-6 text-orange-500" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900">Address</h3>
                                    <p className="text-gray-600">
                                        123 Business Street<br />
                                        Suite 100<br />
                                        City, State 12345
                                    </p>
                                </div>
                            </div>

                            <div className="flex items-start space-x-4">
                                <div className="flex-shrink-0">
                                    <Clock className="w-6 h-6 text-orange-500" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-medium text-gray-900">Business Hours</h3>
                                    <p className="text-gray-600">
                                        Monday - Friday: 9:00 AM - 6:00 PM<br />
                                        Saturday: 10:00 AM - 4:00 PM<br />
                                        Sunday: Closed
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Contact Form */}
                    <div className="bg-white rounded-lg shadow-lg p-8">
                        <h2 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                        
                        <Formik
                            initialValues={initialValues}
                            validationSchema={validationSchema}
                            onSubmit={handleSubmit}
                        >
                            {({ isValid, touched, errors }) => (
                                <Form className="space-y-6">
                                    {/* Name Field */}
                                    <div>
                                        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                            Name *
                                        </label>
                                        <Field
                                            type="text"
                                            id="name"
                                            name="name"
                                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                                                touched.name && errors.name ? 'border-orange-500' : 'border-gray-300'
                                            }`}
                                            placeholder="Enter your full name"
                                        />
                                        <ErrorMessage name="name" component="div" className="mt-1 text-sm text-orange-500" />
                                    </div>

                                    {/* Email Field */}
                                    <div>
                                        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                                            Email *
                                        </label>
                                        <Field
                                            type="email"
                                            id="email"
                                            name="email"
                                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                                                touched.email && errors.email ? 'border-orange-500' : 'border-gray-300'
                                            }`}
                                            placeholder="Enter your email address"
                                        />
                                        <ErrorMessage name="email" component="div" className="mt-1 text-sm text-orange-500" />
                                    </div>

                                    {/* Phone Field */}
                                    <div>
                                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                                            Phone *
                                        </label>
                                        <Field
                                            type="tel"
                                            id="phone"
                                            name="phone"
                                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                                                touched.phone && errors.phone ? 'border-orange-500' : 'border-gray-300'
                                            }`}
                                            placeholder="Enter your phone number"
                                        />
                                        <ErrorMessage name="phone" component="div" className="mt-1 text-sm text-orange-500" />
                                    </div>

                                    {/* Subject Field */}
                                    <div>
                                        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                                            Subject *
                                        </label>
                                        <Field
                                            type="text"
                                            id="subject"
                                            name="subject"
                                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 ${
                                                touched.subject && errors.subject ? 'border-orange-500' : 'border-gray-300'
                                            }`}
                                            placeholder="Enter the subject of your message"
                                        />
                                        <ErrorMessage name="subject" component="div" className="mt-1 text-sm text-orange-500" />
                                    </div>

                                    {/* Message Field */}
                                    <div>
                                        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                                            Message *
                                        </label>
                                        <Field
                                            as="textarea"
                                            id="message"
                                            name="message"
                                            rows={5}
                                            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-vertical ${
                                                touched.message && errors.message ? 'border-orange-500' : 'border-gray-300'
                                            }`}
                                            placeholder="Enter your message here..."
                                        />
                                        <ErrorMessage name="message" component="div" className="mt-1 text-sm text-orange-500" />
                                    </div>

                                    {/* Submit Button */}
                                    <div>
                                        <button
                                            type="submit"
                                            disabled={isSubmitting || !isValid}
                                            className="w-full bg-orange-500 text-white py-3 px-4 rounded-md font-medium hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                        >
                                            {isSubmitting ? (
                                                <div className="flex items-center justify-center">
                                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                    Sending Message...
                                                </div>
                                            ) : (
                                                'Send Message'
                                            )}
                                        </button>
                                    </div>
                                </Form>
                            )}
                        </Formik>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactUs;
