import { useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

type NavOption = 'Delivery' | 'Pickup' | 'Stores' | 'Coming Soon';

interface DeliveryNavProps {
  defaultSelected?: NavOption;
  onOptionChange?: (option: NavOption) => void;
}

export default function ResNavbar({
  onOptionChange
}: DeliveryNavProps) {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  
  // Initialize selected based on URL parameter
  const [selected, setSelected] = useState<NavOption>(() => {
    const typeParam = searchParams.get('type');
    if (typeParam === 'delivery') return 'Delivery';
    if (typeParam === 'pickup') return 'Pickup';
    return 'Delivery'; // Default
  });

  const handleSelect = (option: NavOption) => {
    setSelected(option);
    
    // Update URL with the new service type
    const currentParams = new URLSearchParams(searchParams);
    currentParams.set('type', option.toLowerCase());
    
    // Navigate to the updated URL
    navigate(`/all-restaurants?${currentParams.toString()}`);
    
    // Call the callback if provided
    onOptionChange?.(option);
  };

  const options: NavOption[] = ['Delivery', 'Pickup', 'Stores', 'Coming Soon'];

  return (
    <div className="sticky top-16 z-40 flex md:border-b md:shadow-md shadow-none overflow-x-auto  md:p-0 border-none border-gray-200 w-full bg-gray-50">
      <div className='md:px-20 px-3 md:bg-gray-50 bg-white flex md:space-x-8  space-x-2 md:py-1 py-2 justify-start items-center'>
        {/* Back button - hidden on mobile, visible on tablet and above */}
        <button
          onClick={() => navigate("/")}
          className="hidden md:flex items-center justify-center bg-orange-500 w-8 h-8 mt-4 text-gray-600 text-white hover:bg-orange-600 rounded-full transition-colors mr-4"
          title="Go back"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        {options.map((option) => (
          <button
            key={option}
            className={`md:py-2 cursor-pointer md:pt-5 md:px-0 py-0 px-5 whitespace-nowrap md:text-sm  text-[14px] font-medium md:relative  md:block flex justify-center items-center md:h-full h-7 ${selected === option
              ? 'md:text-black md:border-none text-primary border border-primary rounded-3xl md:bg-gray-50 bg-orange-50'
              : 'text-gray-500 hover:text-gray-800 md:bg-gray-50 bg-white md:border-none md:rounded-one border border-gray-300 rounded-2xl'
              }`}
            onClick={() => handleSelect(option)}
          >
            {option === 'Coming Soon' ? (
              <span className="md:inline-block block  md:px-3 md:py-1 py-0 px-5 w-full rounded-full text-sm md:bg-orange-100 text-primary">
                {option}
              </span>
            ) : (
              option
            )}
            {selected === option && (
              <div className="md:absolute md:-bottom-1 md:left-0 md:w-full md:h-0.5 md:bg-primary" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}

// Example usage:
export function Demo() { // Added export
  const handleOptionChange = (option: NavOption) => { // Fixed type
    console.log(`Selected option: ${option}`);
  };

  return (
    <div className="p-4 md:shadow-sm shadow-none bg-white">
      <ResNavbar
        onOptionChange={handleOptionChange}
      />
    </div>
  );
}
