import contactUsModel from "../models/contactUs.js";
import recieveMail from '../middlewares/recievemail.js'


export const postRecieveEmail = async (req, res) => {
    try {
        const { fullName, email, phone,message, toEmail } = req.body
        const recievemail = await new contactUsModel({ fullName,phone, email, message })
        const recieverData = recievemail.save();
        await recieveMail(email,`<h4>${fullName}</h4> <h4>${company}</h4><h4> ${phone}</h4>
        <p>${message}</p> `, toEmail);
        return res.json({ message: `link send to patronworks email ${recieverData}` })

    } catch (error) {
        res.send("An error occured");
        console.log(error);
    }

}



export const polarisRecieveEmail = async (req, res) => {
    try {
        const { fullName, email, phone,message, toEmail ,company,helpSelect } = req.body
        const recievemail = await new contactUsModel({ fullName,phone, email, message , helpSelect,company})
        const recieverData = recievemail.save();
       
        await recieveMail(email, `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Contact Request</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background-color: #f7f9fc;
                    margin: 0;
                    padding: 40px;
                }
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    background-color: #ffffff;
                    border-radius: 8px;
                    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
                    overflow: hidden;
                    border: 1px solid #e6e6e6;
                }
                .header {
                    background-color: #1da4dc;
                    padding: 20px;
                    color: #ffffff;
                    text-align: center;
                }
                .header h1 {
                    margin: 0;
                    font-size: 24px;
                    font-weight: 500;
                }
                .content {
                    padding: 20px 30px;
                }
                .content h4 {
                    margin: 10px 0;
                    font-size: 16px;
                    color: #333333;
                }
                .content p {
                    margin-top: 20px;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #555555;
                }
                .footer {
                    background-color: #f1f1f1;
                    padding: 15px 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #999999;
                    border-top: 1px solid #e6e6e6;
                }
                .company {
                    margin-top: 20px;
                    padding-top: 15px;
                    border-top: 1px solid #dddddd;
                }
                .company h4 {
                    color: #1da4dc;
                    text-align: right;
                }
                .details {
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <h1>Contact Request</h1>
                </div>
                <div class="content">
                    <div class="details">
                        <h4><strong>Full Name:</strong> ${fullName}</h4>
                        <h4><strong>Email:</strong> ${email}</h4>
                        <h4><strong>Phone:</strong> ${phone}</h4>
                        <h4><strong>Help Needed:</strong> ${helpSelect}</h4>
                    </div>
                    <div class="company">
                        <h4><strong>Company:</strong> ${company}</h4>
                    </div>
                    <p>${message}</p>
                </div>
                <div class="footer">
                    &copy; ${new Date().getFullYear()} Polaris Technology Group. All rights reserved.
                </div>
            </div>
        </body>
        </html>
        
    `, toEmail);
    
        return res.json({ message: `link send to patronworks email ${recieverData}` })

    } catch (error) {
        res.send("An error occured");
        console.log(error);
    }

}


