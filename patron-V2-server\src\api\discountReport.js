import orderitem from '../models/orderitem.js';
import {employee} from '../models/employee.js';

export const getMonthlyDiscountReport = async (req, res) => {
  try {
    const { userId, employeeId, startDate, endDate } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    let query = { 
      userId: userId,
      
      $or: [
        { discountAmount: { $gt: 0 } },
        { couponOfferAmount: { $gt: 0 } }
      ]
    };
    
    if (employeeId) {
      query.employeeId = employeeId;
    }
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    } else {
      // Default to today + previous 29 days (30 days total)
      const today = new Date();
      const endOfToday = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59, 999);
      const startDate30DaysAgo = new Date(today.getTime() - (29 * 24 * 60 * 60 * 1000)); // 29 days ago
      const startOf30DaysAgo = new Date(startDate30DaysAgo.getFullYear(), startDate30DaysAgo.getMonth(), startDate30DaysAgo.getDate(), 0, 0, 0, 0);
      
      query.createdAt = {
        $gte: startOf30DaysAgo,
        $lte: endOfToday
      };
    }

    const orders = await orderitem.find(query)
      .populate({
        path: 'employeeId',
        model: 'Employee',
        select: 'firstName lastName userName'
      })
      .sort({ createdAt: -1 }); // Sort by date descending (newest first)

    const discountTransactions = [];
    
    orders.forEach(order => {
      // Handle regular discounts
      if (order.discountAmount > 0) {
        // Determine cashier name
        let cashierName = 'Admin';
        if (order.employeeId) {
          const emp = order.employeeId;
          cashierName = emp.firstName && emp.lastName 
            ? `${emp.firstName} ${emp.lastName}` 
            : emp.userName || 'Employee';
        }

        // Get discount type - handle both correct spelling and typo
        let discountTypeName = 'General Discount';
        if (order.discountType) {
          // Try correct spelling first, then fallback to typo version
          discountTypeName = order.discountType.discountType || 
                           order.discountType.discoutType || 
                           'General Discount';
        }

        // Get reason
        let reason = 'No reason provided';
        if (order.discountType && order.discountType.reason) {
          reason = order.discountType.reason;
        }

        discountTransactions.push({
          transactionId: order.OrderNumber,
          discountType: discountTypeName,
          amount: parseFloat((order.discountAmount || 0).toFixed(2)),
          cashier: cashierName,
          reason: reason,
          timestamp: order.createdAt,
        });
      }

      // Handle coupon discounts
      if (order.couponOfferAmount > 0 && order.couponOffer && order.couponOffer.length > 0) {
        // Determine cashier name
        let cashierName = 'Admin';
        if (order.employeeId) {
          const emp = order.employeeId;
          cashierName = emp.firstName && emp.lastName 
            ? `${emp.firstName} ${emp.lastName}` 
            : emp.userName || 'Employee';
        }

        // Get coupon discount details
        const couponData = order.couponOffer[0]; 
        let discountTypeName = 'Coupon Discount';
        let reason = 'Coupon Applied';

        if (couponData) {
          // Get discount type from coupon
          if (couponData.discountType) {
            discountTypeName = couponData.discountType;
          }
          
          // Get series name as reason
          if (couponData.selectedDiscount && couponData.selectedDiscount.series) {
            reason = couponData.selectedDiscount.series;
          }
        }

        discountTransactions.push({
          transactionId: order.OrderNumber,
          discountType: discountTypeName,
          amount: parseFloat((order.couponOfferAmount || 0).toFixed(2)),
          cashier: cashierName,
          reason: reason,
          timestamp: order.createdAt,
        });
      }
    });

    // Calculate summary
    const totalDiscountAmount = discountTransactions.reduce((sum, transaction) => 
      sum + transaction.amount, 0
    );

    const totalTransactions = discountTransactions.length;

    // Group by discount type for analysis
    const discountByType = discountTransactions.reduce((acc, transaction) => {
      const type = transaction.discountType;
      if (!acc[type]) {
        acc[type] = {
          count: 0,
          totalAmount: 0
        };
      }
      acc[type].count += 1;
      acc[type].totalAmount += transaction.amount;
      return acc;
    }, {});

    // Group by cashier for analysis
    const discountByCashier = discountTransactions.reduce((acc, transaction) => {
      const cashier = transaction.cashier;
      if (!acc[cashier]) {
        acc[cashier] = {
          count: 0,
          totalAmount: 0
        };
      }
      acc[cashier].count += 1;
      acc[cashier].totalAmount += transaction.amount;
      return acc;
    }, {});

    res.json({
      discountTransactions,
    //   summary: {
    //     totalTransactions,
    //     totalDiscountAmount: parseFloat(totalDiscountAmount.toFixed(2)),
    //     discountByType: Object.keys(discountByType).map(type => ({
    //       type,
    //       count: discountByType[type].count,
    //       totalAmount: parseFloat(discountByType[type].totalAmount.toFixed(2))
    //     })),
    //     discountByCashier: Object.keys(discountByCashier).map(cashier => ({
    //       cashier,
    //       count: discountByCashier[cashier].count,
    //       totalAmount: parseFloat(discountByCashier[cashier].totalAmount.toFixed(2))
    //     }))
    //   },
      period: {
        startDate: query.createdAt.$gte,
        endDate: query.createdAt.$lte
      }
    });
    
  } catch (error) {
    console.error('Error in getMonthlyDiscountReport:', error);
    res.status(500).json({ error: error.message });
  }
};