
import { chatMessages, chatUser, newChat } from "../models/chatModal.js";


 // creating chat between user and customer 
// export const accessChat = async (req, res) => {
//   let newParticipant;
//   let participantId;
  
//   if (!req.body._id) {
//       if (!req.body.email) {
//           return res.status(422).json({ message: "email is required" });
//       }
//       const { email, createdby, name } = req.body;
//       const user = new chatUser({ email, createdby, name });
//       newParticipant = await user.save();
//   }

//   if (newParticipant) {
//       participantId = newParticipant._id;
//   } else {
//       const isChat = await newChat.findOne({ _id: req.body._id }).populate("user").populate("Admin", "name email");
//       if (isChat) {
//           const FullChat = isChat;
//           return res.status(200).json({ chat: FullChat });
//       }
//       return res.status(400).json({ message: "not found" });
//   }

//   let chatData = {
//       user: participantId,
//       Admin: req.body.createdby,
//       admin: req.body.admin || false,
//       chatEnable: req.body.chatEnable || false
//   };

//   try {
//       const newChatData = new newChat(chatData);
//       const createdChat = await newChatData.save();
//       const createdChatId = createdChat._id;
//       const FullChat = await newChat.findOne({ _id: createdChatId }).populate("user").populate("Admin", "-password");

//       res.status(200).json({ chat: FullChat });
//   } catch (error) {
//       res.status(400);
//       throw new Error(error.message);
//   }
// };


// export const accessChat = async (req, res) => {
//   try {
//     let participantId;

//     // Check if email is provided
//     if (!req.body.email) {
//       return res.status(422).json({ message: "email is required" });
//     }

//     // Check if participant with provided email already exists
//     const existingParticipant = await chatUser.findOne({ email: req.body.email });
//     if (existingParticipant) {
//       participantId = existingParticipant._id;
//     } else {
//       // Create a new participant if not found
//       const { email, createdby, name } = req.body;
//       const user = new chatUser({ email, createdby, name });
//       const newParticipant = await user.save();
//       participantId = newParticipant._id;
//     }

//     // Check if chat with provided user ID already exists
//     const existingChat = await newChat.findOne({ user: participantId });
//     if (existingChat) {
//       // If chat exists, return the existing chat in the response
//       const FullChat = await newChat.findOne({ _id: existingChat._id }).populate("user").populate("Admin", "-password");
//       return res.status(200).json({ chat: FullChat });
//     }

//     // Create a new chat if chat doesn't exist
//     const chatData = {
//       user: participantId,
//       Admin: req.body.createdby,
//       admin: req.body.admin || false,
//       chatEnable: req.body.chatEnable || false
//     };

//     const newChatData = new newChat(chatData);
//     const createdChat = await newChatData.save();
//     const createdChatId = createdChat._id;
//     const FullChat = await newChat.findOne({ _id: createdChatId }).populate("user").populate("Admin", "-password");

//     res.status(200).json({ chat: FullChat });
//   } catch (error) {
//     res.status(400).json({ error: error.message });
//   }
// };

export const accessChat = async (req, res) => {
  try {
    let participantId;

    // Check if email is provided
    if (!req.body.email) {
      return res.status(422).json({ message: "email is required" });
    }

    // Check if participant with provided email already exists
    let existingParticipant = await chatUser.findOne({ email: req.body.email });
    if (!existingParticipant) {
      // Create a new participant if not found
      const { email, createdby, name } = req.body;
      const user = new chatUser({ email, createdby, name });
      existingParticipant = await user.save();
    }
    
    participantId = existingParticipant._id;

    // Check if chat with provided user ID already exists
    let existingChat = await newChat.findOne({ user: participantId }).populate("user").populate("Admin", "-password");
    if (!existingChat) {
      // Create a new chat if chat doesn't exist
      const chatData = {
        user: participantId,
        Admin: req.body.createdby,
        admin: req.body.admin || false,
        chatEnable: req.body.chatEnable || false
      };

      const newChatData = new newChat(chatData);
      existingChat = await newChatData.save();
      existingChat = await newChat.findOne({ _id: existingChat._id }).populate("user").populate("Admin", "-password");
    }

    // Return the chat in the response
    res.status(200).json({ chat: existingChat });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
};








  // show customer chat 

  export const fetchCustomerChats = async (req, res) => {
    const { Admin } = req.query;
  
    if (!Admin || Admin === "null" || Admin === "undefined") {
      return res.status(422).json({ error: "admin id is required" });
    }
  
    try {
      const results = await newChat.find({
        $and: [{ chatEnable: true },{ admin: false }, { Admin: req.query.Admin }],
      })
        .populate("user")
        .populate("Admin", "name email")
        .sort({ updatedAt: -1 });
  
      const chatIds = results.map(chat => chat._id);
      const unreadCounts = await Promise.all(
        chatIds.map(chatId =>
          chatMessages.countDocuments({
            chat: chatId,
            readBy: { $ne: Admin }
          })
        )
      );
  
      const chatsWithUnreadCount = results.map((chat, index) => ({
        ...chat.toObject(),
        unreadCount: unreadCounts[index],
      }));
  
      res.status(200).send(chatsWithUnreadCount);
    } catch (error) {
      res.status(400);
      throw new Error(error.message);
    }
  };

// sho admin chat 
  export const fetchAdminChats = async (req, res) => {
    const { Admin } = req.query;
  
    if (!Admin || Admin === "null" || Admin === "undefined") {
      return res.status(422).json({ error: "admin id is required" });
    }
  
    try {
      const results = await newChat.find({
        $and: [{ chatEnable: true },{ admin: true }, { Admin: req.query.Admin }],
      })
        .populate("user")
        .populate("Admin", "name email")
        .sort({ updatedAt: -1 });
  
      const chatIds = results.map(chat => chat._id);
      const unreadCounts = await Promise.all(
        chatIds.map(chatId =>
          chatMessages.countDocuments({
            chat: chatId,
            readBy: { $ne: Admin }
          })
        )
      );
  
      const chatsWithUnreadCount = results.map((chat, index) => ({
        ...chat.toObject(),
        unreadCount: unreadCounts[index],
      }));
  
      res.status(200).send(chatsWithUnreadCount);
    } catch (error) {
      res.status(400);
      throw new Error(error.message);
    }
  };
  export const fetchsuperAdminChats = async (req, res) => {
    const { userId } = req.query;

    if (!userId || userId === "null" || userId === "undefined") {
        return res.status(422).json({ error: "User ID is required" });
    }

    try {
      const results = await newChat.find({
        $and: [{ chatEnable: true },{ admin: true }, { user: userId }],
      }).populate("user")
      .populate("Admin", "name email")
      .sort({ updatedAt: -1 });
        const chatIds = results.map(chat => chat._id);
        const unreadCounts = await Promise.all(
            chatIds.map(chatId =>
                chatMessages.countDocuments({
                    chat: chatId,
                    readBy: { $ne: userId }
                })
            )
        );

        const chatsWithUnreadCount = results.map((chat, index) => ({
            ...chat.toObject(),
            unreadCount: unreadCounts[index],
        }));

        res.status(200).send(chatsWithUnreadCount);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
};


  
  

  // delete chat 
  export const deleteChat = async (req, res) => {
    try {
      const { chatId } = req.params;
  
      const chat = await newChat.findById(chatId);
      if (!chat) {
        return res.status(404).json({ error: "Chat not found" });
      }
  
      await chatMessages.deleteMany({ chat: chatId });
  
      await newChat.findByIdAndDelete(chatId);
  
      res.json({ message: "Chat and associated messages deleted successfully" });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: "Internal server error" });
    }
  };
  
  


                                            //   messages portion  //

// sending messages 

export const sendMessage = async (req, res) => {

    const { content, chatId, sender, senderId } = req.body;
  
    if (!chatId) {
      return res.status(400).json({error:"chat id is required"});
    }
    const checkChat = await newChat.findById({_id:chatId})
    if (checkChat.chatEnable == false) {
       await newChat.findByIdAndUpdate({_id:chatId}, { chatEnable: true })
    }
    var newMessage = {
      sender,
      content,
      chat: chatId,
      senderId
    };
    let newMessageData = new chatMessages(newMessage)
    try {
      var message = await newMessageData.save()
      let messageDetail = await chatMessages.findById({_id:message._id}).populate("chat")
      const updateChat = await newChat.findByIdAndUpdate({_id:chatId}, { latestMessage: message.content })
      res.json(messageDetail);
    } catch (error) {
     res.status(400).json({error:"something went wrong!"})
    }
  };

// delete message 
  export const deleteMessage = async (req, res) => {
    const { messageId } = req.params;

    try {
        if (!messageId) {
            return res.status(400).json({ error: "messageId is required" });
        }
        await chatMessages.findByIdAndDelete({ _id: messageId });

        res.json({ success: true });
    } catch (error) {
        res.status(400).json({ error: "something went wrong!" });
    }
};


  
//   // show all messages 
  
  export const allMessages = async (req, res) => {
    let {_id}=req.query
    if(!_id){
      return res.status(400).json({error:"user id is required"})
    }
    let filter = { readBy: { $ne: _id } };
    let {chatId}=req.params
    if(req.params.chatId) {
      filter.chat= chatId ;
    } 
    try {
       await chatMessages.updateMany( filter, { $push: { readBy: _id } });
      const messages = await chatMessages.find({ chat: req.params.chatId })
        .populate({ path: "chat", populate: { path: "Admin", modal: "superUser", select: "name email" } })
        .populate({ path: "chat", populate: { path: "user", modal: "chatUser" } })
      res.json(messages);
    } catch (error) {
      res.status(400).json({error:"something went wrong !"})
    }
  };