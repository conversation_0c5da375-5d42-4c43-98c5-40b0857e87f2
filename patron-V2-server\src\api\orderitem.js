import sendMail from "../middlewares/send-email.js";
import IngredientModel from "../models/ingredients.js";
import orderitem from "../models/orderitem.js";
import Product from "../models/product.js";
import { User } from "../models/User.js";
import customer from "../models/customer.js";
import moment from "moment";
import mongoose from "mongoose";
import { Types } from "mongoose";

export const getOrderItemByUserId = async (req, res) => {
  let filter = {};
  if (req.query.userId) {
    filter = { userId: req.query.userId.split(",") };
  } else if (req.query.orderId) {
    filter = { orderId: req.query.orderId.split(",") };
  } else if (req.query.orderStatus) {
    filter = { orderStatus: req.query.orderStatus.split(",") };
  }
  let data = await orderitem
    .find(filter)
    .populate("customerId")
    .populate("selectedModifiers")
    .populate("paymentType")
    .populate("table")
    .populate({
      path: "orderId",
      populate: { path: "employeeId", model: "employee" },
      populate: { path: "recieptId", model: "reciept" },
    })
    .populate("ReservedTable");
  // let data = await orderitem.find(filter).populate({ path: "product", populate: { path: "categoryId", model: "category" } }).populate('customerId').populate("selectedModifiers").populate('paymentType').populate('table').populate({ path: "orderId", populate: { path: "employeeId", model: "employee" }, populate: { path: "recieptId", model: "reciept" } }).populate('ReservedTable')
  const populatedDocuments = await Promise.all(
    data.map(async (doc) => {
      if (
        Array.isArray(doc.product) &&
        doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
      ) {
        const products = await Product.find({ _id: { $in: doc.product } });
        doc.product = products;
      }
      return doc;
    })
  );
  res.send(populatedDocuments);
};

export const getCustomersOrderItemByUserId = async (req, res) => {
  // Start with the customerId filter to ensure only orders with customerId are fetched
  let filter = { customerId: { $exists: true, $ne: null } };

  // Add additional filters if provided in the query
  if (req.query.userId) {
    filter.userId = { $in: req.query.userId.split(",") };
  } else if (req.query.orderId) {
    filter.orderId = { $in: req.query.orderId.split(",") };
  } else if (req.query.orderStatus) {
    filter.orderStatus = { $in: req.query.orderStatus.split(",") };
  }

  try {
    // First fetch all order items that match the filter (including customerId check)
    const data = await orderitem
      .find(filter)
      .populate({
        path: "product",
        populate: { path: "categoryId", model: "category" },
      })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate({
        path: "orderId",
        populate: [
          { path: "employeeId", model: "employee" },
          { path: "recieptId", model: "reciept" },
        ],
      })
      .populate("ReservedTable");

    // Filter out online orders (keep only POS orders)
    const PosOrders = data.filter(
      (item) =>
        item.orderStatus !== "online" &&
        item.customerId !== null &&
        item.customerId !== undefined
    );

    // Additional population if needed
    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );

    // Final filter to ensure no null customerIds slipped through
    const finalResults = populatedDocuments.filter(
      (doc) => doc.customerId !== null && doc.customerId !== undefined
    );

    res.send(finalResults);
  } catch (error) {
    console.error("Error fetching order items:", error);
    res.status(500).send({ message: "Failed to fetch order items" });
  }
};

export const getOrderItemsWithRefundData = async (req, res) => {
  const userId = req.query.userId;
  try {
    if (!userId) {
      return res.status(400).send("User ID is required");
    }
    const data = await orderitem
      .find({ userId, refundData: { $exists: true, $not: { $size: 0 } } })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate("employeeId")
      .populate({
        path: "orderId",
        populate: { path: "employeeId", model: "employee" },
        populate: { path: "recieptId", model: "reciept" },
      })
      .populate("ReservedTable")
      .populate("userId");
    // .find({ userId, refundData: { $exists: true, $not: { $size: 0 } } }).populate({ path: "product", populate: { path: "categoryId", model: "category" } }).populate('customerId').populate("selectedModifiers").populate('paymentType').populate('table').populate({ path: "orderId", populate: { path: "employeeId", model: "employee" }, populate: { path: "recieptId", model: "reciept" } }).populate('ReservedTable')

    let PosOrders = data?.filter((item) => item.orderStatus != "online");
    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );
    res.send(populatedDocuments);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getOrderItemslastmonth = async (req, res) => {
  console.log("kalkjd ajkdsklfj ");
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    let lastMonthEndDate = moment()
      .subtract(2, "months")
      .endOf("month")
      .toDate();
    const todayEndDate = moment().endOf("day").toDate();
    let data = await orderitem
      .find({
        userId,
        createdAt: { $gte: lastMonthEndDate, $lte: todayEndDate },
      })
      // .populate({ path: "product", populate: { path: "categoryId", model: "category" } })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate({
        path: "orderId",
        populate: { path: "employeeId", model: "employee" },
        populate: { path: "recieptId", model: "reciept" },
      })
      .populate("ReservedTable");

    let PosOrders = data?.filter((item) => item.orderStatus != "online");
    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );
    res.send(populatedDocuments);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getItemsSaleslastmonth = async (req, res) => {
  const { userId, reportType, startDate, endDate } = req.query;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    let fromDate, toDate;
    const today = moment();

    switch (reportType) {
      case "Daily":
        fromDate = moment().startOf("day");
        toDate = moment().endOf("day");
        break;
      case "Weekly":
        fromDate = moment().subtract(7, "days").startOf("day");
        toDate = today.endOf("day");
        break;
      case "Monthly":
        fromDate = moment().subtract(1, "months").startOf("month");
        toDate = today.endOf("day");
        break;
      case "date":
        fromDate = moment(startDate).startOf("day");
        toDate = moment(endDate).endOf("day");
        break;
      default:
        fromDate = moment().subtract(2, "months").endOf("month");
        toDate = today.endOf("day");
    }

    const data = await orderitem
      .find({
        userId,
        createdAt: { $gte: fromDate.toDate(), $lte: toDate.toDate() },
      })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate({
        path: "orderId",
        populate: [
          { path: "employeeId", model: "employee" },
          { path: "recieptId", model: "reciept" },
        ],
      })
      .populate("ReservedTable");

    const PosOrders = data.filter((item) => item.orderStatus !== "online");

    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((p) => p instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );

    // Combine product data
    let totalQty = 0;
    let totalPrice = 0;
    const productMap = new Map();

    for (const order of populatedDocuments) {
      const { productWithQty = [], product = [] } = order;
      totalQty++;

      productWithQty.forEach((item) => {
        const key = item.productId;
        const existing = productMap.get(key);

        if (!existing) {
          const prod = product.find((p) => p._id.toString() === key);
          if (prod) {
            productMap.set(key, {
              productId: key,
              qty: item.qty,
              name: prod.name,
              remainingqty: prod.totalQuantity,
              price: prod.price * item.qty,
              unitPrice: prod.price,
            });
          }
        } else {
          existing.qty += item.qty;
          existing.price += existing.unitPrice * item.qty;
          productMap.set(key, existing);
        }
      });
    }

    const combinedProducts = Array.from(productMap.values()).sort(
      (a, b) => b.qty - a.qty
    );
    totalPrice = combinedProducts.reduce((sum, item) => sum + item.price, 0);

    res.send({
      totalQty,
      totalPrice,
      combinedProducts,
    });
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getModifierSalesReport = async (req, res) => {
  const { userId, reportType, startDate, endDate } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  try {
    const today = moment();
    let fromDate, toDate;

    switch (reportType) {
      case "Daily":
        fromDate = today.clone().startOf("day");
        toDate = today.clone().endOf("day");
        break;
      case "Weekly":
        fromDate = today.clone().subtract(7, "days").startOf("day");
        toDate = today.clone().endOf("day");
        break;
      case "Monthly":
        fromDate = today.clone().subtract(1, "months").startOf("month");
        toDate = today.clone().endOf("day");
        break;
      case "date":
        fromDate = moment(startDate).startOf("day");
        toDate = moment(endDate).endOf("day");
        break;
      default:
        fromDate = today.clone().subtract(2, "months").endOf("month");
        toDate = today.clone().endOf("day");
    }

    const reportData = await orderitem.aggregate([
      {
        $match: {
          userId: new mongoose.Types.ObjectId(userId),
          createdAt: { $gte: fromDate.toDate(), $lte: toDate.toDate() },
          orderStatus: { $ne: "online" },
        },
      },
      {
        $lookup: {
          from: "products",
          localField: "productWithQty.productId",
          foreignField: "_id",
          as: "productDetails",
        },
      },
      {
        $unwind: { path: "$productWithQty", preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: {
          path: "$selectedModifiers",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: "$selectedModifiers.data",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: null,
          products: {
            $push: {
              productId: "$productWithQty.productId",
              qty: "$productWithQty.qty",
              price: {
                $multiply: [
                  "$productWithQty.qty",
                  { $arrayElemAt: ["$productDetails.price", 0] },
                ],
              },
              name: { $arrayElemAt: ["$productDetails.name", 0] },
              remainingqty: {
                $arrayElemAt: ["$productDetails.totalQuantity", 0],
              },
            },
          },
          modifiers: {
            $push: {
              _id: "$selectedModifiers.data._id",
              name: "$selectedModifiers.data.names",
              qty: "$selectedModifiers.data.quantity",
              price: {
                $cond: {
                  if: {
                    $and: [
                      { $ne: ["$selectedModifiers.data.quantity", null] },
                      { $ne: ["$selectedModifiers.data.price", null] },
                    ],
                  },
                  then: {
                    $multiply: [
                      "$selectedModifiers.data.quantity",
                      "$selectedModifiers.data.price",
                    ],
                  },
                  else: null,
                },
              },
            },
          },
        },
      },
      {
        $project: {
          combinedProducts: {
            $reduce: {
              input: "$products",
              initialValue: [],
              in: {
                $let: {
                  vars: {
                    existing: {
                      $filter: {
                        input: "$$value",
                        as: "item",
                        cond: { $eq: ["$$item.productId", "$$this.productId"] },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: { $gt: [{ $size: "$$existing" }, 0] },
                      then: {
                        $map: {
                          input: "$$value",
                          as: "item",
                          in: {
                            $cond: {
                              if: {
                                $eq: ["$$item.productId", "$$this.productId"],
                              },
                              then: {
                                productId: "$$item.productId",
                                name: "$$item.name",
                                remainingqty: "$$item.remainingqty",
                                qty: { $add: ["$$item.qty", "$$this.qty"] },
                                unitPrice: {
                                  $divide: [
                                    { $add: ["$$item.price", "$$this.price"] },
                                    { $add: ["$$item.qty", "$$this.qty"] },
                                  ],
                                },
                                price: {
                                  $add: ["$$item.price", "$$this.price"],
                                },
                              },
                              else: "$$item",
                            },
                          },
                        },
                      },
                      else: {
                        $concatArrays: [
                          "$$value",
                          [
                            {
                              productId: "$$this.productId",
                              name: "$$this.name",
                              remainingqty: "$$this.remainingqty",
                              qty: "$$this.qty",
                              unitPrice: {
                                $cond: [
                                  { $ne: ["$$this.qty", 0] },
                                  { $divide: ["$$this.price", "$$this.qty"] },
                                  0,
                                ],
                              },
                              price: "$$this.price",
                            },
                          ],
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          combinedModifiers: {
            $reduce: {
              input: "$modifiers",
              initialValue: [],
              in: {
                $let: {
                  vars: {
                    existing: {
                      $filter: {
                        input: "$$value",
                        as: "item",
                        cond: { $eq: ["$$item._id", "$$this._id"] },
                      },
                    },
                  },
                  in: {
                    $cond: {
                      if: { $gt: [{ $size: "$$existing" }, 0] },
                      then: {
                        $map: {
                          input: "$$value",
                          as: "item",
                          in: {
                            $cond: {
                              if: { $eq: ["$$item._id", "$$this._id"] },
                              then: {
                                _id: "$$item._id",
                                name: "$$item.name",
                                qty: { $add: ["$$item.qty", "$$this.qty"] },
                                unitPrice: {
                                  $divide: [
                                    { $add: ["$$item.price", "$$this.price"] },
                                    { $add: ["$$item.qty", "$$this.qty"] },
                                  ],
                                },
                                price: {
                                  $add: ["$$item.price", "$$this.price"],
                                },
                              },
                              else: "$$item",
                            },
                          },
                        },
                      },
                      else: {
                        $concatArrays: [
                          "$$value",
                          [
                            {
                              _id: "$$this._id",
                              name: "$$this.name",
                              qty: "$$this.qty",
                              unitPrice: {
                                $cond: [
                                  { $ne: ["$$this.qty", 0] },
                                  { $divide: ["$$this.price", "$$this.qty"] },
                                  0,
                                ],
                              },
                              price: "$$this.price",
                            },
                          ],
                        ],
                      },
                    },
                  },
                },
              },
            },
          },
          totalQty: { $sum: "$products.qty" },
          totalPrice: { $sum: "$products.price" },
          totalModifierQty: { $sum: "$modifiers.qty" },
          totalModifierPrice: { $sum: "$modifiers.price" },
        },
      },
    ]);

    // Fallback default
    const result =
      reportData.length > 0
        ? reportData[0]
        : {
          combinedProducts: [],
          combinedModifiers: [],
          totalQty: 0,
          totalPrice: 0,
          totalModifierQty: 0,
          totalModifierPrice: 0,
        };

    // Sort results
    result.combinedProducts.sort((a, b) => b.qty - a.qty);
    result.combinedModifiers = result.combinedModifiers
      .filter((m) => m.qty !== null && m.price !== null && m.unitPrice !== null)
      .sort((a, b) => b.qty - a.qty);

    res.send(result);
  } catch (error) {
    console.error("Error in getModifierSalesReport:", error);
    res.status(500).send("Internal Server Error");
  }
};

export const getItemsSalesLastMonth = async (req, res) => {
  const {
    userId,
    reportType,
    startDate,
    endDate,
    paymentIds,
    customerId,
    employeeId,
    status,
  } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  try {
    let fromDate, toDate;
    const today = moment();

    switch (reportType) {
      case "Today":
      case "Daily":
        fromDate = moment().startOf("day");
        toDate = moment().endOf("day");
        break;
      case "Weekly":
        fromDate = moment().subtract(7, "days").startOf("day");
        toDate = today.endOf("day");
        break;
      case "Monthly":
        fromDate = moment().subtract(1, "months").startOf("month");
        toDate = today.endOf("day");
        break;
      case "date":
        fromDate = moment(startDate).startOf("day");
        toDate = moment(endDate).endOf("day");
        break;
      default:
        fromDate = moment().subtract(2, "months").startOf("day");
        toDate = today.endOf("day");
    }

    const matchStage = {
      userId: new mongoose.Types.ObjectId(userId),
      createdAt: { $gte: fromDate.toDate(), $lte: toDate.toDate() },
      orderStatus: { $ne: "online" },
    };

    if (customerId) {
      const customerIdArray =
        typeof customerId === "string"
          ? customerId.split(",")
          : Array.isArray(customerId)
            ? customerId
            : [];

      const validCustomerIds = customerIdArray.filter((id) =>
        mongoose.Types.ObjectId.isValid(id)
      );

      if (validCustomerIds.length === 1) {
        matchStage.customerId = new mongoose.Types.ObjectId(
          validCustomerIds[0]
        );
      } else if (validCustomerIds.length > 1) {
        matchStage.customerId = {
          $in: validCustomerIds.map((id) => new mongoose.Types.ObjectId(id)),
        };
      }
    }

    if (employeeId) {
      const employeeIdArray =
        typeof employeeId === "string"
          ? employeeId.split(",")
          : Array.isArray(employeeId)
            ? employeeId
            : [];

      const validEmployeeIds = employeeIdArray.filter((id) =>
        mongoose.Types.ObjectId.isValid(id)
      );

      if (validEmployeeIds.length === 1) {
        matchStage.employeeId = new mongoose.Types.ObjectId(
          validEmployeeIds[0]
        );
      } else if (validEmployeeIds.length > 1) {
        matchStage.employeeId = {
          $in: validEmployeeIds.map((id) => new mongoose.Types.ObjectId(id)),
        };
      }
    }

    if (paymentIds) {
      const paymentIdArray =
        typeof paymentIds === "string"
          ? paymentIds.split(",")
          : Array.isArray(paymentIds)
            ? paymentIds
            : [];

      const validPaymentIds = paymentIdArray.filter((id) =>
        mongoose.Types.ObjectId.isValid(id)
      );

      if (validPaymentIds.length > 0) {
        matchStage.paymentType = {
          $in: validPaymentIds.map((id) => new mongoose.Types.ObjectId(id)),
        };
      }
    }

    const commonPipeline = [
      { $match: matchStage },
      {
        $addFields: {
          orderTotalPrice: {
            $subtract: [
              {
                $sum: {
                  $map: {
                    input: "$productWithQty",
                    as: "item",
                    in: { $multiply: ["$$item.qty", "$$item.price"] },
                  },
                },
              },
              {
                $add: [
                  { $ifNull: ["$discountAmount", 0] },
                  { $ifNull: ["$couponOfferAmount", 0] },
                ],
              },
            ],
          },
          totalTax: {
            $sum: {
              $map: {
                input: "$tax",
                as: "taxItem",
                in: "$$taxItem.addtax",
              },
            },
          },
          totalDiscount: {
            $add: [
              { $ifNull: ["$discountAmount", 0] },
              { $ifNull: ["$couponOfferAmount", 0] },
            ],
          },
        },
      },
    ];

    let facetStage;

    if (status === "customers") {
      facetStage = {
        orders: [
          {
            $lookup: {
              from: "customers",
              localField: "customerId",
              foreignField: "_id",
              as: "customerInfo",
            },
          },
          {
            $unwind: {
              path: "$customerInfo",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              customerName: {
                $cond: {
                  if: { $ifNull: ["$customerId", false] },
                  then: {
                    $concat: [
                      { $ifNull: ["$customerInfo.FirstName", ""] },
                      " ",
                      { $ifNull: ["$customerInfo.LastName", ""] },
                    ],
                  },
                  else: "Guest",
                },
              },
            },
          },
          {
            $project: {
              _id: 1,
              createdAt: 1,
              customerName: 1,
              customerId: 1,
              totalPrice: "$orderTotalPrice",
              totalTax: 1,
              discountAmount: { $ifNull: ["$discountAmount", 0] },
              couponOfferAmount: { $ifNull: ["$couponOfferAmount", 0] },
              totalDiscount: 1,
            },
          },
        ],
        summary: [
          {
            $group: {
              _id: null,
              totalOrders: { $sum: 1 },
              totalRevenue: { $sum: "$orderTotalPrice" },
              totalTax: { $sum: "$totalTax" },
              totalDiscount: { $sum: "$totalDiscount" },
            },
          },
          {
            $project: {
              _id: 0,
              totalOrders: 1,
              totalRevenue: 1,
              totalTax: 1,
              totalDiscount: 1,
            },
          },
        ],
        customerBreakdown: [
          {
            $lookup: {
              from: "customers",
              localField: "customerId",
              foreignField: "_id",
              as: "customerInfo",
            },
          },
          {
            $unwind: {
              path: "$customerInfo",
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              customerName: {
                $cond: {
                  if: { $ifNull: ["$customerId", false] },
                  then: {
                    $concat: [
                      { $ifNull: ["$customerInfo.FirstName", ""] },
                      " ",
                      { $ifNull: ["$customerInfo.LastName", ""] },
                    ],
                  },
                  else: "Guest",
                },
              },
            },
          },
          {
            $group: {
              _id: "$customerId",
              customerName: { $first: "$customerName" },
              totalOrders: { $sum: 1 },
              totalRevenue: { $sum: "$orderTotalPrice" },
              totalTax: { $sum: "$totalTax" },
              totalDiscount: { $sum: "$totalDiscount" },
            },
          },
          {
            $project: {
              _id: 0,
              customerId: "$_id",
              customerName: 1,
              totalOrders: 1,
              totalRevenue: 1,
              totalTax: 1,
              totalDiscount: 1,
            },
          },
        ],
      };
    } else {
      commonPipeline.push(
        {
          $addFields: {
            effectivePaymentType: {
              $ifNull: [
                "$paymentType",
                mongoose.Types.ObjectId("000000000000000000000000"),
              ],
            },
          },
        },
        {
          $lookup: {
            from: "paymentlists",
            localField: "effectivePaymentType",
            foreignField: "_id",
            as: "paymentTypeInfo",
          },
        },
        {
          $unwind: {
            path: "$paymentTypeInfo",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $addFields: {
            paymentTypeName: {
              $ifNull: ["$paymentTypeInfo.name", "Cash"],
            },
          },
        }
      );

      facetStage = {
        orders: [
          {
            $project: {
              _id: 1,
              createdAt: 1,
              paymentType: "$paymentTypeName",
              totalPrice: "$orderTotalPrice",
              totalTax: 1,
              discountAmount: { $ifNull: ["$discountAmount", 0] },
              couponOfferAmount: { $ifNull: ["$couponOfferAmount", 0] },
              totalDiscount: 1,
            },
          },
        ],
        summary: [
          {
            $group: {
              _id: null,
              totalOrders: { $sum: 1 },
              totalRevenue: { $sum: "$orderTotalPrice" },
              totalTax: { $sum: "$totalTax" },
              totalDiscount: { $sum: "$totalDiscount" },
            },
          },
          {
            $project: {
              _id: 0,
              totalOrders: 1,
              totalRevenue: 1,
              totalTax: 1,
              totalDiscount: 1,
            },
          },
        ],
        paymentTypeBreakdown: [
          {
            $group: {
              _id: "$paymentTypeName",
              totalOrders: { $sum: 1 },
              totalRevenue: { $sum: "$orderTotalPrice" },
              totalTax: { $sum: "$totalTax" },
              totalDiscount: { $sum: "$totalDiscount" },
            },
          },
          {
            $project: {
              _id: 0,
              paymentType: "$_id",
              totalOrders: 1,
              totalRevenue: 1,
              totalTax: 1,
              totalDiscount: 1,
            },
          },
        ],
        taxBreakdown: [
          { $unwind: "$tax" },
          {
            $group: {
              _id: "$tax.name",
              totalTax: { $sum: "$tax.addtax" },
            },
          },
          {
            $project: {
              _id: 0,
              taxName: "$_id",
              totalTax: 1,
            },
          },
        ],
      };
    }

    const pipeline = [...commonPipeline, { $facet: facetStage }];

    const [result] = await orderitem.aggregate(pipeline);
    const {
      orders,
      summary,
      paymentTypeBreakdown,
      customerBreakdown,
      taxBreakdown,
    } = result;

    res.send({
      orders,
      summary: summary[0] || {
        totalOrders: 0,
        totalRevenue: 0,
        totalTax: 0,
        totalDiscount: 0,
      },
      breakdown:
        status === "customers" ? customerBreakdown : paymentTypeBreakdown,
      taxReport: taxBreakdown || [],
    });
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};
export const getLastOrderByUserId = async (req, res) => {
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    let lastOrder = await orderitem
      .findOne({ userId })
      .sort({ createdAt: -1 })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate("ReservedTable")
      .populate({
        path: "orderId",
        populate: [
          { path: "employeeId", model: "employee" },
          { path: "recieptId", model: "reciept" },
        ],
      });

    if (
      lastOrder &&
      Array.isArray(lastOrder.product) &&
      lastOrder.product.every((item) => item instanceof mongoose.Types.ObjectId)
    ) {
      const products = await Product.find({ _id: { $in: lastOrder.product } });
      lastOrder.product = products;
    }

    if (!lastOrder) {
      return res.status(404).send("No orders found for this user");
    }

    res.send(lastOrder);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};
export const getDueOrders = async (req, res) => {
  const { userId, startDate, endDate } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  // Build basic match condition
  const matchCondition = {
    userId: new mongoose.Types.ObjectId(userId),
    orderStatus: { $ne: "online" },
  };

  // If date filters provided
  if (startDate && endDate) {
    matchCondition.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    };
  } else if (startDate) {
    matchCondition.createdAt = { $gte: new Date(startDate) };
  } else if (endDate) {
    matchCondition.createdAt = { $lte: new Date(endDate) };
  }

  try {
    const dueOrders = await orderitem.aggregate([
      { $match: matchCondition },
      {
        $match: { dueamount: { $gt: 0 } }, // Only orders that have due amount
      },
      {
        $lookup: {
          from: "customers",
          localField: "customerId",
          foreignField: "_id",
          as: "customerInfo",
        },
      },
      { $unwind: { path: "$customerInfo", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          customerName: {
            $cond: {
              if: { $ifNull: ["$customerId", false] },
              then: {
                $concat: [
                  { $ifNull: ["$customerInfo.FirstName", ""] },
                  "",
                  { $ifNull: ["$customerInfo.LastName", ""] },
                ],
              },
              else: "Guest",
            },
          },
        },
      },
      {
        $project: {
          // _id: 1,rs
          OrderNumber: 1,
          createdAt: 1,
          grandTotal: 1,
          dueamount: 1,
          customerName: 1,
        },
      },
      { $sort: { createdAt: -1 } }, // Optional: newest orders first
    ]);

    res.send(dueOrders);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getLastWeekOrders = async (req, res) => {
  const { userId, startDate, endDate } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  const matchCondition = {
    userId: new mongoose.Types.ObjectId(userId),
    orderStatus: { $ne: "online" },
  };

  if (!startDate && !endDate) {
    const today = new Date();
    // const lastWeek = new Date();
    // lastWeek.setDate(today.getDate() - 7);

    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(today.getMonth() - 1);
    const endOfToday = new Date();
    endOfToday.setHours(23, 59, 59, 999);

    matchCondition.createdAt = {
      $gte: oneMonthAgo,
      $lte: endOfToday,
    };
  } else if (startDate && endDate) {
    matchCondition.createdAt = {
      $gte: new Date(startDate),
      $lte: new Date(endDate),
    };
  } else if (startDate) {
    matchCondition.createdAt = { $gte: new Date(startDate) };
  } else if (endDate) {
    matchCondition.createdAt = { $lte: new Date(endDate) };
  }

  try {
    console.log("Match Condition:", matchCondition);
    const orders = await orderitem.aggregate([
      { $match: matchCondition },

      {
        $lookup: {
          from: "order",
          localField: "orderId",
          foreignField: "_id",
          as: "orderDetails",
        },
      },
      { $unwind: { path: "$orderDetails", preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          operatorName: {
            $cond: {
              if: { $ifNull: ["$orderDetails.operator", false] },
              then: "$orderDetails.operator",
              else: "Admin",
            },
          },
        },
      },
      {
        $addFields: {
          Tips: {
            $cond: {
              if: { $ifNull: ["$orderDetails.Tips", false] },
              then: "$orderDetails.Tips",
              else: 0,
            },
          },
        },
      },
      {
        $addFields: {
          EmployeeName: {
            $cond: {
              if: { $ifNull: ["$orderDetails.EmployeeName", false] },
              then: "$orderDetails.EmployeeName",
              else: "",
            },
          },
        },
      },
      {
        $project: {
          OrderNumber: 1,
          grandTotal: 1,
          createdAt: 1,
          operatorName: 1,
          Tips: 1,
          EmployeeName: 1,
        },
      },
      { $sort: { createdAt: -1 } },
    ]);

    res.send(orders);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getPosKitchenOrders = async (req, res) => {
  const { userId } = req.query;

  if (!userId) return res.status(400).send("User ID is required");

  const matchCondition = {
    userId: new mongoose.Types.ObjectId(userId),
    orderStatus: { $ne: "online" },
  };

  const endTime = new Date();
  endTime.setHours(23, 59, 59, 999);

  const startTime = new Date(endTime.getTime() - 48 * 60 * 60 * 1000);

  matchCondition.createdAt = {
    $gte: startTime,
    $lte: endTime,
  };

  try {
    console.log("Match Condition:", matchCondition);
    const orders = await orderitem.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: "tables",
          localField: "table",
          foreignField: "_id",
          as: "table",
        },
      },
      {
        $lookup: {
          from: "customers",
          localField: "customerId",
          foreignField: "_id",
          as: "customerId",
        },
      },
      { $unwind: { path: "$customerId", preserveNullAndEmptyArrays: true } },
      {
        $project: {
          OrderNumber: 1,
          customerId: 1,
          product: 1,
          dropStatus: 1,
          kitchenStatus: 1,
          Status: 1,
          orderNo: 1,
          createdAt: 1,
          table: "$table",
        },
      },
    ]);

    res.send(orders);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getOrderItemsLastWeek = async (req, res) => {
  console.log("asdkfjalskdj fklasjdfklhakldjsh");
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    const todayEndDate = moment().endOf("day").toDate();
    const lastWeekStartDate = moment()
      .subtract(6, "days")
      .startOf("day")
      .toDate();

    let data = await orderitem
      .find({
        userId,
        createdAt: { $gte: lastWeekStartDate, $lte: todayEndDate },
      })
      // .populate({ path: "product", populate: { path: "categoryId", model: "category" } })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate("employeeId")
      .populate({
        path: "orderId",
        populate: { path: "employeeId", model: "employee" },
        populate: { path: "recieptId", model: "reciept" },
      })
      .populate("ReservedTable");

    let PosOrders = data?.filter((item) => item.orderStatus != "online");

    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );
    res.send(populatedDocuments);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

// Getting the current month's order item count: 
export const getOrderItemsCurrentMonth = async (req, res) => {
  const userId = req.query.userId;

  if (!userId) {
    return res.status(400).send("User ID is required");
  }

  try {
    const startOfMonth = moment().startOf("month").toDate();
    const endOfToday = moment().endOf("day").toDate();

    const orders = await orderitem.find({
      userId,
      orderStatus: { $ne: "online" },
      createdAt: { $gte: startOfMonth, $lte: endOfToday },
    });

    const totalOrders = orders.length;

    let totalPayment = 0;
    let refundedOrders = 0;

    orders.forEach(order => {
      totalPayment += order.recieveamount || 0;

      if (Array.isArray(order.refundData) && order.refundData.length > 0) {
        refundedOrders += 1;
      }
    });

    res.send({
      totalOrders,
      totalPayment,
      refundedOrders
    });
  } catch (error) {
    console.error("Error fetching POS order count:", error);
    res.status(500).send("Internal Server Error");
  }
};

export const getOrderItemsByDateRange = async (req, res) => {
  try {
    const userId = req.query.userId;
    const startDate = new Date(req.query.startDate);
    const endDate = new Date(req.query.endDate);
    if (!userId) {
      return res
        .status(400)
        .send("User ID, start date, and end date are required");
    }
    let allOrders = await orderitem
      .find({ userId })
      // .populate({ path: "product", populate: { path: "categoryId", model: "category" } })
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate({
        path: "orderId",
        populate: { path: "employeeId", model: "employee" },
        populate: { path: "recieptId", model: "reciept" },
      })
      .populate("ReservedTable");

    const startDateTime = moment(startDate).startOf("day").toDate();
    const endDateTime = moment(endDate).endOf("day").toDate();
    let filteredData = allOrders.filter((order) => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDateTime && orderDate <= endDateTime;
    });
    let PosOrders = filteredData?.filter(
      (item) => item.orderStatus != "online"
    );

    const populatedDocuments = await Promise.all(
      PosOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );
    res.send(populatedDocuments);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getOrderItemOrderStatus = async (req, res) => {
  try {
    let filter = {};
    if (req.query.userId) {
      filter = { userId: req.query.userId.split(",") };
    }

    let data = await orderitem
      .find(filter)
      .populate("customerId")
      .populate("selectedModifiers")
      .populate("paymentType")
      .populate("table")
      .populate({
        path: "orderId",
        populate: {
          path: "employeeId",
          model: "employee",
        },
        populate: {
          path: "recieptId",
          model: "reciept",
        },
      })
      .populate("ReservedTable");

    let onlineOrders = data.filter((item) => item.orderStatus == "online");

    const populatedDocuments = await Promise.all(
      onlineOrders.map(async (doc) => {
        if (
          Array.isArray(doc.product) &&
          doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
        ) {
          const products = await Product.find({ _id: { $in: doc.product } });
          doc.product = products;
        }
        return doc;
      })
    );

    res.send(populatedDocuments);
  } catch (error) {
    console.error(error);
    res.status(500).send("Internal Server Error");
  }
};

export const getOrderItemById = async (req, res) => {
  let data = await orderitem
    .findOne(req.params)
    .populate("customerId")
    .populate("paymentType")
    .populate("table")
    .populate({
      path: "orderId",
      populate: { path: "employeeId", model: "employee" },
      populate: { path: "recieptId", model: "reciept" },
    })
    .populate("ReservedTable");
  // let data = await orderitem.findOne(req.params).populate({ path: "product", populate: { path: "categoryId", model: "category" } }).populate('customerId').populate('paymentType').populate('table').populate({ path: "orderId", populate: { path: "employeeId", model: "employee" }, populate: { path: "recieptId", model: "reciept" } }).populate('ReservedTable')
  const populatedDocuments = await Promise.all(
    data.map(async (doc) => {
      if (
        Array.isArray(doc.product) &&
        doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
      ) {
        const products = await Product.find({ _id: { $in: doc.product } });
        doc.product = products;
      }
      return doc;
    })
  );
  res.send(populatedDocuments);
};

export const getOrderItems = async (req, res) => {
  let data = await orderitem
    .find(req.params)
    .populate("customerId")
    .populate("paymentType")
    .populate("table")
    .populate({
      path: "orderId",
      populate: { path: "employeeId", model: "employee" },
    })
    .populate("ReservedTable");
  // let data = await orderitem.find(req.params).populate({ path: "product", populate: { path: "categoryId", model: "category" } }).populate('customerId').populate('paymentType').populate('table').populate({ path: "product", populate: { path: "userId", model: "user" } }).populate({ path: "orderId", populate: { path: "employeeId", model: "employee" } }).populate('ReservedTable')
  const populatedDocuments = await Promise.all(
    data.map(async (doc) => {
      if (
        Array.isArray(doc.product) &&
        doc.product.every((item) => item instanceof mongoose.Types.ObjectId)
      ) {
        const products = await Product.find({ _id: { $in: doc.product } });
        doc.product = products;
      }
      return doc;
    })
  );
  res.send(populatedDocuments);
};

export const postOrderItem = async (req, res) => {
  // return  console.log("Request Body:", req.body);
  const { orderId, table, product, selectedModifiers, recieveamount, grandTotal, loyalityOffer, orderNo, couponOffer, ReservedTable, points, orderStatus, taxValue, productWithQty, priceExclTax, lineValueExclTax, lineValueTax, lineValue, units, text, customerId, dueamount, createdAt, updatedAt, userId, paymentType, split, tax, Color, customername, employeeId, vehicle, taxfake, OrderNo, kitchenStatus, refundData, refundTotal, PaymentStatus, deliveryfee, surCharge, discountType, discountAmount, couponOfferAmount, tip } = req.body;
  const lastOrder = await orderitem.findOne({ userId }, {}, { sort: { '_id': -1 } });
  const lastOrderCount = lastOrder ? (lastOrder.OrderNumber || 0) : 0;
  let numericCount
  if (lastOrderCount != 0) {
    numericCount = parseInt(lastOrderCount.slice(2), 10) + 1;
  } else {
    numericCount = Number("00001");
  }
  const OrderNumber = `OR${numericCount.toString().padStart(4, "0")}`;

  const data = await new orderitem({
    orderId, table, product, orderStatus, grandTotal, selectedModifiers, recieveamount, loyalityOffer, orderNo, couponOffer, points, ReservedTable, taxValue, productWithQty, priceExclTax, lineValueExclTax, lineValueTax, lineValue, units, text, customerId, dueamount, createdAt, updatedAt, userId, paymentType, split, employeeId, tax, Color, customername, vehicle, taxfake, OrderNo, kitchenStatus, OrderNumber, refundData, refundTotal, PaymentStatus, deliveryfee, surCharge, discountType, discountAmount, couponOfferAmount, tip
  }).populate('table');
  let prod = []
  let ingredientsData = []
  prod = product
  await data.save().then(async (result) => {
    const updatedData = await orderitem.findById(result._id).populate('table').populate('employeeId').populate('paymentType').populate('tip.employeeId')
    const customerPoints = 5;
    const customerById = await customer.findById(customerId)
    if (Array.isArray(prod) && prod.length > 0) {
      prod.map(async (item) => {
        const products = await Product.findById({ _id: item._id })
          .populate('userId')
          .populate({
            path: "ingredient",
            populate: { path: "ingredient.ingredientId", model: "ingredientsModel" }
          });
        // await Product.findByIdAndUpdate({ _id: products._id }, { $set: { "totalQuantity": products.totalQuantity - item.quantity } })
        const totalQuantity = parseFloat(products.totalQuantity);
        const quantity = parseFloat(item.quantity);

        if (!isNaN(totalQuantity) && !isNaN(quantity)) {
          // Perform the subtraction only if both values are valid numbers
          const updatedTotalQuantity = totalQuantity - quantity;
          await Product.findByIdAndUpdate(item._id, {
            $set: { totalQuantity: updatedTotalQuantity },
          });
        }
        let filteredProductsName = [];
        let userEmail = products.userId.email;
        if (products.totalQuantity <= 5) {
          filteredProductsName.push(products.name);
        }
        if (products.ingredient && products.ingredient.length > 0) {
          const ingredients = [];
          for (const ingredientItem of products.ingredient) {
            if (ingredientItem.ingredientId) {
              const ingredientId = ingredientItem.ingredientId;
              const user = await User.findOne({ _id: userId });
              const ingredient = await IngredientModel.findOne({
                _id: ingredientId,
              });
              if (
                ingredient?.stockHistory &&
                ingredient?.stockHistory.length > 0
              ) {
                const sortedStockHistory = ingredient?.stockHistory.sort(
                  (a, b) => {
                    const dateA = new Date(a.expiry);
                    const dateB = new Date(b.expiry);
                    return dateA - dateB;
                  }
                );
                let remainingQuantity =
                  ingredientItem.quantity * item.quantity;
                let stocksToRemove = [];
                for (const stockItem of sortedStockHistory) {
                  if (remainingQuantity <= 0) {
                    break;
                  }
                  if (stockItem.stock >= remainingQuantity) {
                    stockItem.stock -= remainingQuantity;
                    remainingQuantity = 0;
                  } else {
                    remainingQuantity -= stockItem.stock;
                    stockItem.stock = 0;
                  }
                  if (stockItem.stock === 0) {
                    stocksToRemove.push(stockItem);
                  }
                }
                for (const stockToRemove of stocksToRemove) {
                  const indexToRemove =
                    ingredient?.stockHistory.indexOf(stockToRemove);
                  if (indexToRemove !== -1) {
                    ingredient?.stockHistory.splice(indexToRemove, 1);
                  }
                }
                let totalStock = 0;

                for (const stockItem of ingredient.stockHistory) {
                  totalStock += stockItem.stock;
                }
                if (totalStock < ingredient.ThresholdLevel) {
                  const email = user.email;
                  await sendMail(
                    email,
                    "Stock quantity are decrease ",
                    `<html>

      <head>
      <style>
      
      .logo{
      width: 30%;
      }
      a{
      color:blue;
      cursor:pointer;
      text-decoration:none;}
      .maindivdata{
      padding:2rem 4rem;
      border: 1px solid lightgray;}
      
      .client{
       color: white;
       font-weight: 700;
       display: flex;
       font-size: 25px;
       width: 100%;
       justify-content:center;
       padding-top: 10rem;
       padding-left:20px;
      }
   
   .power{
   font-size:12px;
   color:gray;
   }
   p{
   font-size:16px;
       font-family: 'Poppins', sans-serif;
   
   }
  
   
      .container{
      width:50%;
      margin:auto;
          font-family: 'Poppins', sans-serif;
   
   
      
      }
      .infologo{
        background:transparent;
        border:none;
      }
      .shortimg{
        width:20px;
        height:20px;
        }
  
   h3{
          font-family: 'Poppins', sans-serif;
   
   }
   span{
          font-family: 'Poppins', sans-serif;
   
   }
   h5{
          font-family: 'Poppins', sans-serif;
   
   }
      @media screen and (max-width:900px) {
      .container{
      width:100%;
      margin:0px;
      
      }
      .client{
       color: white;
       font-weight: 700;
       display: grid;
       font-size: 25px;
       width: 100%;
       padding-top: 10rem;
       padding-left:10px;
      }
    .maindivdata{
      padding:2rem 10px;
      }
      .btn{
        font-size:12px
                }
                
      
      }
      
      </style>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
   
      </head>
         <body>
         <div class="container">
         <div style="font-family: Arial, Helvetica, sans-serif; ">
         <div style="width: auto; height: 4rem;background-color: rgb(6, 138, 245); ">
         
             
         
         </div>
         <div class="maindivdata">
       <div class="top" style="  display:flex; 
       justify-content:center !important; align-items:center;"> 
   <img class="image" style=" justify-self:center ; margin-left:20%; display:flex; justify-content:center !important; align-items:center; width:60%;" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg">
       </div>
                  <p style="font-size: 1rem; margin-bottom: 2rem;">Dear <strong>${email}</strong></p>
   
             <p style=" margin-bottom: 2rem;">We are excited to have you join us at PatronWorks.</p>
                       
             <p style=" margin-bottom: 2rem;">Please Add Stock on <strong>${ingredient.IngredientName}<strong> </p>
   
         
            
             <p style=" margin-bottom: 2rem;">We look forward to serving you.</p>
             <p style=" margin-bottom: 2rem;"> If you have any questions or require assistance, don't hesitate to contact our support team at <strong><EMAIL>. </strong></p>
             <p style=" margin-bottom: 2rem;">Thank you for choosing PatronWorks for your business!</p>
             
    
    <hr>
      
   <div style="display:flex; justify-content:space-between; margin-top:1rem;">
      <div>
      <img style="width:60%" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281267/WhatsApp_Image_2023-07-25_at_3.33.32_PM_xnwnuy.jpg">
      </div>
      <div style="display:flex; margin-left:45%;">
      <a style="margin-right:10px; href="https://www.linkedin.com/company/patronworks/">
        <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn">
      </a>
      
      <a href="https://www.facebook.com/patronworks">
        <img class="shortimg" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook">
      </a>
    </div>

      </div>
            
      
           </div>
      </div>
      </div>
      </body>
      </html>`
                  );
                  console.log(
                    `Quantity is less in amount. Total stock: ${totalStock}, ThresholdLevel: ${ingredient.ThresholdLevel}`
                  );
                }
                await ingredient.save();
              }

              ingredients.push(ingredient);
            }
          }
          console.log("ingredients: ", ingredients);
          return ingredients;
        }

        if (
          products.totalQuantity <= 5 &&
          userEmail &&
          filteredProductsName
        ) {
          sendMail(
            userEmail,
            "Low Stock Alerts",
            `<h2 style="background-color: #f1f1f1; padding: 20px;width:50%">These Products Are  Low  In  Stock</h2><br><h3 style="background-color: #f1f1f1; width:60%">${filteredProductsName}</h3>`
          );
        }
      });
    } else {
      console.warn('Invalid or empty "product" array');
    }

    if (customerById) {
      // const customerdata = await customer.findByIdAndUpdate(customerById, { $set: { "CustomerLoyalty.Points": !isNaN(customerById?.CustomerLoyalty?.Points) + customerPoints } })
      const customerdata = await customer.findByIdAndUpdate(
        customerById,
        {
          $set: {
            "CustomerLoyalty.Points":
              (!isNaN(customerById?.CustomerLoyalty?.Points)
                ? customerById.CustomerLoyalty.Points
                : 0) + customerPoints,
            "CustomerLoyalty.Visits":
              (!isNaN(customerById?.CustomerLoyalty?.Visits)
                ? customerById.CustomerLoyalty.Visits
                : 0) + 1,
            "CustomerLoyalty.LastVisit": new Date(),
          },
        },
        { new: true } // return the updated document
      );

      console.log("customerAfterAddedPoints", customerdata);
      const finalData = await orderitem.findById(result._id).populate('table').populate('employeeId').populate('customerId').populate('paymentType').populate('tip.employeeId')
      res.json({
        orderId: finalData.orderId,
        table: finalData.table,
        product: finalData.product,
        OrderNumber: finalData.OrderNumber,
        recieveamount: finalData.recieveamount,
        dueamount: finalData.dueamount,
        displayStatus: finalData.displayStatus,
        loyalityOffer: finalData.loyalityOffer,
        couponOffer: finalData.couponOffer,
        orderNo: finalData.orderNo,
        points: finalData.points,
        split: finalData.split,
        Status: finalData.Status,
        OrderNo: finalData.OrderNo,
        taxValue: finalData.taxValue,
        productWithQty: finalData.productWithQty,
        priceExclTax: finalData.priceExclTax,
        productPrice: finalData.productPrice,
        lineValueExclTax: finalData.lineValueExclTax,
        lineValueTax: finalData.lineValueTax,
        lineValue: finalData.lineValue,
        grandTotal: finalData.grandTotal,
        units: finalData.units,
        text: finalData.text,
        tax: finalData.tax,
        userId: finalData.userId,
        employeeId: finalData.employeeId,
        customerId: finalData.customerId,
        paymentType: finalData.paymentType,
        createdAt: finalData.createdAt,
        updatedAt: finalData.updatedAt,
        orderStatus: finalData.orderStatus,
        ReservedTable: finalData.ReservedTable,
        Color: finalData.Color,
        customername: finalData.customername,
        vehicle: finalData.vehicle,
        refundData: finalData.refundData,
        refundTotal: finalData.refundTotal,
        kitchenStatus: finalData.kitchenStatus,
        PaymentStatus: finalData.PaymentStatus,
        deliveryfee: finalData.deliveryfee,
        surCharge: finalData.surCharge,
        tip: finalData?.tip

      })
    } else {
      res.json({
        orderId: updatedData.orderId,
        product: updatedData.product,
        table: updatedData.table,
        recieveamount: updatedData.recieveamount,
        OrderNumber: updatedData.OrderNumber,
        dueamount: updatedData.dueamount,
        displayStatus: updatedData.displayStatus,
        loyalityOffer: updatedData.loyalityOffer,
        couponOffer: updatedData.couponOffer,
        orderNo: updatedData.orderNo,
        points: updatedData.points,
        split: updatedData.split,
        Status: updatedData.Status,
        OrderNo: updatedData.OrderNo,
        taxValue: updatedData.taxValue,
        productWithQty: updatedData.productWithQty,
        priceExclTax: updatedData.priceExclTax,
        grandTotal: updatedData.grandTotal,
        productPrice: updatedData.productPrice,
        lineValueExclTax: updatedData.lineValueExclTax,
        lineValueTax: updatedData.lineValueTax,
        lineValue: updatedData.lineValue,
        units: updatedData.units,
        text: updatedData.text,
        tax: updatedData.tax,
        userId: updatedData.userId,
        customerId: updatedData.customerId,
        employeeId: updatedData.employeeId,
        paymentType: updatedData.paymentType,
        createdAt: updatedData.createdAt,
        updatedAt: updatedData.updatedAt,
        orderStatus: updatedData.orderStatus,
        ReservedTable: updatedData.ReservedTable,
        Color: updatedData.Color,
        customername: updatedData.customername,
        vehicle: updatedData.vehicle,
        refundData: updatedData.refundData,
        refundTotal: updatedData.refundTotal,
        kitchenStatus: updatedData.kitchenStatus,
        PaymentStatus: updatedData.PaymentStatus,
        deliveryfee: updatedData.deliveryfee,
        surCharge: updatedData.surCharge,
        tip: updatedData?.tip

      })
    }
  }).catch(err => {
    res.status(400).send('unable to save database');
    console.log(err)
  })
}
export const updateOrderItem = async (req, res) => {
  try {
    // const orderId = req.params.orderId;
    const updatedData = await orderitem
      .findByIdAndUpdate(
        { _id: req.body._id },
        { $set: req.body },
        { new: true }
      )
      .populate("table")
      .populate("customerId");

    if (updatedData) {
      res.send({
        message: "Order item data updated successfully",
        updatedData,
      });
    } else {
      res.send({
        message: "Order item data cannot be updated successfully",
        updatedData,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: "Internal server error" });
  }
};
export const updatedisplayOrders = async (req, res) => {
  try {
    const orderId = req.params._id;
    const updatedData = await orderitem.findByIdAndUpdate(
      orderId,
      { $set: req.body },
      { new: true }
    );
    if (updatedData) {
      res.send({
        message: "Order item data updated successfully",
        updatedData,
      });
    } else {
      res.send({
        message: "Order item data cannot be updated successfully",
        updatedData,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).send({ message: "Internal server error" });
  }
};

export const deleteOrderItem = async (req, res) => {
  console.log(req.params);
  let data = await orderitem.deleteOne(req.params);
  if (data) res.send({ message: "orderitem data delete successfully" });
  else res.send({ message: "orderitem data cannot delete successfully" });
};
