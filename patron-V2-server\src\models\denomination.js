// import mongoose from 'mongoose';

// const billSchema = new mongoose.Schema({
//   denominations: [{
//     value: {
//       type: Number
//     },
//     quantity: {
//       type: Number
//     },
//   }
//   ],
//   totalCashOnhand: {
//     type: Number,
//     required: true
//   },
//   EnteredBy: {
//     type: String,
//     required: true
//   },
//   userId: {
//     type: mongoose.Schema.Types.ObjectId,
//     required: true,
//     ref: 'user'
//   }
// }, { timestamps: true })
// const BillDenomination = mongoose.model('Denominations', billSchema);
// export default BillDenomination


import mongoose from 'mongoose';

const denominationSchema = new mongoose.Schema({
  value: Number,
  quantity: Number,
});

const billSchema = new mongoose.Schema({
  denominations: [denominationSchema],
  totalCashOnhand: {
    type: Number,
    required: true
  },
  EnteredBy: {
    type: String,
    required: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
    ref: 'user'
  },
  type: {
    type: String,
    enum: ['opening_cash','cash_in', 'cash_out', 'drop', 'payout'],
    required: true
  },
  note: {
    type: String,
    default: ''
  },
  overShortAmount: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

const BillDenomination = mongoose.model('Denominations', billSchema);
export default BillDenomination;





