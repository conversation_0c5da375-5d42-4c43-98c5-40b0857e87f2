import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { updateCustomer } from '../redux-store/slices/customerSlice';
import type { AppDispatch } from '../redux-store/store';
import Swal from 'sweetalert2';

interface DeliveryAddressPopupProps {
  isOpen: boolean;
  onClose: () => void;
  currentCustomer: any;
  onAddressUpdate: (address: string) => void;
}

interface AddressData {
  street: string;
  city: string;
  state: string;
  zipcode: string;
}

const DeliveryAddressPopup: React.FC<DeliveryAddressPopupProps> = ({
  isOpen,
  onClose,
  currentCustomer,
  onAddressUpdate
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [loading, setLoading] = useState(false);
  
  // Initialize form data
  const [formData, setFormData] = useState<AddressData>({
    street: '',
    city: '',
    state: '',
    zipcode: ''
  });

  // Check if Address2 exists and populate form
  useEffect(() => {
    if (currentCustomer?.Address2) {
      setFormData({
        street: currentCustomer.Address2.street || '',
        city: currentCustomer.Address2.city || '',
        state: currentCustomer.Address2.state || '',
        zipcode: currentCustomer.Address2.zipcode || ''
      });
    } else {
      // Reset form if no Address2
      setFormData({
        street: '',
        city: '',
        state: '',
        zipcode: ''
      });
    }
  }, [currentCustomer, isOpen]);

  const handleInputChange = (field: keyof AddressData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.street.trim() || !formData.city.trim() || !formData.state.trim() || !formData.zipcode.trim()) {
      Swal.fire({
        title: 'Missing Information',
        text: 'Please fill in all address fields',
        icon: 'warning',
        confirmButtonText: 'OK',
        confirmButtonColor: '#FF5C00',
        timer: 2000,
        timerProgressBar: true
      });
      return;
    }

    try {
      setLoading(true);

      // Update customer with Address2
      await dispatch(updateCustomer({
        userId: currentCustomer._id,
        customerData: {
          Address2: {
            street: formData.street.trim(),
            city: formData.city.trim(),
            state: formData.state.trim(),
            zipcode: formData.zipcode.trim()
          }
        }
      })).unwrap();

      // Create formatted address string for display
      const formattedAddress = `${formData.street}, ${formData.city}, ${formData.state} ${formData.zipcode}`;
      
      // Update the delivery address in parent component
      onAddressUpdate(formattedAddress);

      // Show success message with SweetAlert2
      Swal.fire({
        title: 'Success!',
        text: 'Delivery address saved successfully!',
        icon: 'success',
        confirmButtonText: 'OK',
        confirmButtonColor: '#FF5C00',
        timer: 2000,
        timerProgressBar: true
      });

      // Close popup after successful save
      onClose();
    } catch (error: any) {
      console.error('Error updating delivery address:', error);
      const message = error?.message || error || 'Failed to update delivery address';
      Swal.fire({
        title: 'Error!',
        text: message,
        icon: 'error',
        confirmButtonText: 'OK',
        confirmButtonColor: '#FF5C00',
        timer: 3000,
        timerProgressBar: true
      });
    } finally {
      setLoading(false);
    }
  };

  const hasExistingAddress = currentCustomer?.Address2 && 
    (currentCustomer.Address2.street || currentCustomer.Address2.city || 
     currentCustomer.Address2.state || currentCustomer.Address2.zipcode);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">
            {hasExistingAddress ? 'Update Delivery Address' : 'Add Delivery Address'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Street Address */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Street Address *
            </label>
            <input
              type="text"
              value={formData.street}
              onChange={(e) => handleInputChange('street', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter street address"
              required
            />
          </div>

          {/* City */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              City *
            </label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder="Enter city"
              required
            />
          </div>

          {/* State and Zipcode */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State *
              </label>
              <input
                type="text"
                value={formData.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                placeholder="State"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Zip Code *
              </label>
              <input
                type="text"
                value={formData.zipcode}
                onChange={(e) => handleInputChange('zipcode', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500"
                placeholder="Zip code"
                required
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 disabled:opacity-50"
              disabled={loading}
            >
              {loading ? 'Saving...' : (hasExistingAddress ? 'Update Address' : 'Create Address')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DeliveryAddressPopup;
