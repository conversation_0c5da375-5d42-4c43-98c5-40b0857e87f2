@import "tailwindcss";

@theme {
  --color-primary: #FF5C00;
}

body {
  font-family: 'Poppins', sans-serif;
}

/* Ensure all buttons have cursor pointer */
button {
  cursor: pointer;
}

/* Ensure disabled buttons have not-allowed cursor */
button:disabled {
  cursor: not-allowed;
}

/* Ensure clickable elements have pointer cursor */
[role="button"],
.cursor-pointer,
[onClick] {
  cursor: pointer;
}

/* Custom SweetAlert2 styles */
.swal2-popup-custom {
  border-radius: 16px !important;
  padding: 2rem !important;
}

.swal2-popup-custom .swal2-title {
  font-size: 1.5rem !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  margin-bottom: 1rem !important;
}

.swal2-popup-custom .swal2-html-container {
  margin: 0 !important;
  padding: 0 !important;
}

.swal2-timer-progress-bar {
  background: #FF5C00 !important;
}