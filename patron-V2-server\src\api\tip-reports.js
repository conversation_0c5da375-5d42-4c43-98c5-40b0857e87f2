import orderitem from '../models/orderitem.js';

export const getTipweeklyDReport = async (req, res) => {
  try {
    const { userId, employeeId, startDate, endDate } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'userId is required' });
    }
    
    let query = { userId: userId, 'tip.amount': { $gt: 0 } };
    
    if (employeeId) {
      query['tip.employeeId'] = employeeId;
    }
    
    
    if (startDate || endDate) {
      query.createdAt = {};
      if (startDate) {
        query.createdAt.$gte = new Date(startDate);
      }
      if (endDate) {
        query.createdAt.$lte = new Date(endDate);
      }
    } else {
      const today = new Date();
      const startOfRange = new Date(today);
      startOfRange.setDate(today.getDate() - 29);
      startOfRange.setHours(0, 0, 0, 0);

      const endOfRange = new Date(today);
      endOfRange.setHours(23, 59, 59, 999);
      
      query.createdAt = {
        $gte: startOfRange,
        $lte: endOfRange
      };
    }

    const orders = await orderitem.find(query)
      .populate({
        path: 'paymentType',
        model: 'paymentlist',
        select: 'name' 
      })
      .populate({
        path: 'tip.employeeId',
        model: 'Employee',
        select: 'firstName lastName' 
      })
      .sort({ createdAt: -1 });

    const employeeTips = {};
    let totalCashTips = 0;
    let totalCardTips = 0;

    orders.forEach(order => {
      if (order.tip && order.tip.amount) {
        const empId = order.tip.employeeId?._id?.toString() || 'unknown';
        const paymentType = order.paymentType?.name?.toLowerCase() || 'cash';
        
        if (!employeeTips[empId]) {
          employeeTips[empId] = {
            // employeeId: empId,
            employeeName: order.tip.employeeId ? 
              `${order.tip.employeeId.firstName} ${order.tip.employeeId.lastName}` : 
              'Unknown Employee',
            cashTips: 0,
            cardTips: 0,
            totalTips: 0
          };
        }

        if (paymentType.includes('card')) {
          employeeTips[empId].cardTips += order.tip.amount;
          totalCardTips += order.tip.amount;
        } else {
          employeeTips[empId].cashTips += order.tip.amount;
          totalCashTips += order.tip.amount;
        }
        
        employeeTips[empId].totalTips += order.tip.amount;
      }
    });
    const employeeTipList = Object.values(employeeTips);

    res.json({
      employeeTips: employeeTipList,
      // summary: {
      //   totalCashTips: parseFloat(totalCashTips.toFixed(2)),
      //   totalCardTips: parseFloat(totalCardTips.toFixed(2)),
      //   totalTips: parseFloat((totalCashTips + totalCardTips).toFixed(2)),
      //   totalOrdersWithTips: orders.length
      // }
    });
    
  } catch (error) {
    console.error('Error in getTipweeklyDReport:', error);
    res.status(500).json({ error: error.message });
  }
};