import { Modifier } from "../models/productmodifier.js";

export const getModifiers = async (req, res) => {
    let filter = {}
    if (req.query.userId) {
        filter = { userId: req.query.userId.split(',') }
    } else if (req.query.productId) {
        filter = { productId: req.query.productId.split(',') }
    }
    let modifierData = await Modifier.find(filter).populate("productId")
    res.send(modifierData)
}
export const getModifiersById = async (req, res) => {
    let modifierData = await Modifier.findOne(req.params).populate("productId")
    res.send(modifierData)
}

//post
export const addModifiers = async (req, res) => {
    const newCategory = await new Modifier({ Modifier: req.body.Modifier, productId: req.body.productId, userId: req.body.userId, isActive:req.body.isActive }).populate('productId');
    const savedCategory = await newCategory.save();
    res.json(savedCategory);
};

//put 
// const Modifier = mongoose.model('Modifier', ModifierSchema);

// Define the API endpoint for updating quantity
// app.put('/updateQuantity', async (req, res) => {
// export const updateModifiers =async (req, res) => {
//   try {
//     const productId = req.body.productId; // Assuming you pass productId in the request body
//     const subcategoryName = req.body.Modifier
//     ; // Assuming you pass subcategoryName in the request body
//     const newQuantity = req.body.newQuantity; // Assuming you pass newQuantity in the request body

//     // Find the document that matches the productId and userId
//     console.log("category : ::::::: : ",req.body.productId)
//     console.log("category : ::::::: : ",productId)
//     const category = await Modifier.findOne({ productId: productId });
//     console.log("category : ::::::: : ",category)

//     if (!category) {
//       return res.status(404).json({ error: 'Category not found.' });
//     }

//     // Find the subcategory by name
//     const subcategory = category.categories.find(cat => cat.subcategories.find(subcat => subcat.name === subcategoryName));

//     if (!subcategory) {
//       return res.status(404).json({ error: 'Subcategory not found.' });
//     }

//     // Update the totalQuantity field in the subcategory
//     subcategory.subcategories.forEach(subcat => {
//       if (subcat.name === subcategoryName) {
//         subcat.totalQuantity = newQuantity;
//       }
//     });

//     // Save the updated document
//     await category.save();

//     return res.status(200).json({ message: 'Quantity updated successfully.' });
//   } catch (error) {
//     console.error('Error updating quantity:', error);
//     return res.status(500).json({ error: 'Internal server error.' });
//   }
// };
export const updateModifiers = async (req, res) => {
  try {
    const { productId, userId, isActive } = req.body;

    // Ensure all required fields are provided
    if (!productId || !userId || !req.body.Modifier) {
      return res.status(400).json({ error: 'Missing required fields.' });
    }

    // Find the document that matches the productId and userId
    const category = await Modifier.findOne({ productId: productId, userId: userId });

    if (!category) {
      return res.status(404).json({ error: 'Modifier not found.' });
    }

    // Update isActive field
    category.isActive = isActive;

    // Loop through the modifiers in the request body
    for (const modifier of req.body.Modifier) {
      // Find the existing modifier by name
      let existingModifier = category.Modifier.find(mod => mod.name === modifier.name);

      if (!existingModifier) {
        // If modifier doesn't exist, create it
        existingModifier = { name: modifier.name, properties: modifier.properties };
        category.Modifier.push(existingModifier); // Add new modifier to the array
      } else {
        // Handle properties: add, update, or remove
        // Update or add properties
        for (const prop of modifier.properties) {
          const existingProperty = existingModifier.properties.find(
            propItem => propItem.name === prop.name
          );

          if (!existingProperty) {
            // If the property doesn't exist, add it
            existingModifier.properties.push({
              name: prop.name,
              totalQuantity: prop.totalQuantity,
              price: prop.price
            });
          } else {
            // If the property exists, update it
            existingProperty.totalQuantity = prop.totalQuantity;
            existingProperty.price = prop.price;
          }
        }

        // Remove properties that are not in the request
        existingModifier.properties = existingModifier.properties.filter(
          existingProp => modifier.properties.some(
            prop => prop.name === existingProp.name
          )
        );
      }
    }

    category.Modifier = category.Modifier.filter(existingModifier =>
      req.body.Modifier.some(modifier => modifier.name === existingModifier.name)
    );
    
    // Save the updated category
    const savedModifier = await category.save();
    const populatedModifier = await Modifier.findById(savedModifier._id)
      .populate('productId');

    res.status(200).json({ populatedModifier, message: 'Modifiers updated successfully.', category });
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: 'Internal server error.' });
  }
};







export const putModifiers = async (req, res) => {
    const { _id } = req.params;
    const { name, properties } = req.body;

    const updatedCategory = await Modifier.findOneAndUpdate(
        { _id: _id },
        { name, properties },
        { new: true }
    );
    res.json(updatedCategory);
};

//delete

export const deleteModifiers = async (req, res) => {
    const { id } = req.params;
    await Modifier.deleteOne({ _id: id });
    res.sendStatus(204);
};