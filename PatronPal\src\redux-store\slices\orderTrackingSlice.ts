import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

interface OrderItem {
    _id: string;
    name: string;
    price: number;
    quantity: number;
    image?: string;
    restaurant: {
        name: string;
        _id: string;
    };
    modifiers?: any;
    note?: string;
    isFree?: boolean;
    discountPrice?: number;
    modifierPrice?: number;
}

interface OrderTrackingState {
    currentOrder: {
        items: OrderItem[];
        total: number;
        paymentMethod: string;
        orderType: string;
        orderId?: string;
        timestamp: string;
    } | null;
}

const initialState: OrderTrackingState = {
    currentOrder: null,
};

const orderTrackingSlice = createSlice({
    name: 'orderTracking',
    initialState,
    reducers: {
        setCurrentOrder: (state, action: PayloadAction<{
            items: OrderItem[];
            total: number;
            paymentMethod: string;
            orderType: string;
            orderId?: string;
        }>) => {
            state.currentOrder = {
                ...action.payload,
                timestamp: new Date().toISOString(),
            };
        },
        clearCurrentOrder: (state) => {
            state.currentOrder = null;
        },
    },
});

export const { setCurrentOrder, clearCurrentOrder } = orderTrackingSlice.actions;

// Selectors
export const selectCurrentOrder = (state: { orderTracking: OrderTrackingState }) => 
    state.orderTracking.currentOrder;

export const selectOrderItems = (state: { orderTracking: OrderTrackingState }) => 
    state.orderTracking.currentOrder?.items || [];

export const selectOrderTotal = (state: { orderTracking: OrderTrackingState }) => 
    state.orderTracking.currentOrder?.total || 0;

export default orderTrackingSlice.reducer;
