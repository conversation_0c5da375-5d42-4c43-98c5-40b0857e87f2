/**
 * Utility functions for handling tax-related operations
 */

// Tax interface to match the API response
export interface Tax {
  _id: string;
  name: string;
  taxValue: string;
  userId: string;
  byDefault: boolean;
  active: boolean;
  __v: number;
}

/**
 * Stores restaurant userId in localStorage and fetches tax data
 * @param userId - The restaurant's userId
 * @returns Promise<Tax[]> - Tax data from the API
 */
export const handleRestaurantTaxData = async (userId: string): Promise<Tax[]> => {
  try {
    // Store the restaurant's userId in localStorage
    localStorage.setItem('userid', userId);
    console.log('Stored restaurant userId in localStorage:', userId);

    // Get the base URL from environment variables
    const baseURL = import.meta.env.VITE_LOCAL_API_URL || import.meta.env.VITE_PROD_API_URL;

    // Call tax API with the userId
    const response = await fetch(`${baseURL}/Tax?userId=${userId}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const taxData: Tax[] = await response.json();
    console.log('Tax data for restaurant:', taxData);

    // Calculate and log total tax percentage
    const totalTaxPercentage = calculateTotalTaxPercentage(taxData);
    console.log('Total tax percentage for this restaurant:', totalTaxPercentage + '%');

    // Store tax data in localStorage for later use
    localStorage.setItem('restaurantTaxData', JSON.stringify(taxData));
    localStorage.setItem('totalTaxPercentage', totalTaxPercentage.toString());

    // Trigger a custom event to notify components that tax data has been updated
    window.dispatchEvent(new CustomEvent('taxDataUpdated', {
      detail: { taxData, totalTaxPercentage }
    }));

    return taxData;
  } catch (error) {
    console.error('Error fetching tax data:', error);
    throw error;
  }
};

/**
 * Calculates the total tax percentage from active taxes
 * @param taxes - Array of tax objects
 * @returns number - Total tax percentage
 */
export const calculateTotalTaxPercentage = (taxes: Tax[]): number => {
  const activeTaxes = taxes.filter(tax => tax.active === true);
  const totalTaxPercentage = activeTaxes.reduce((total, tax) => {
    const taxValue = parseFloat(tax.taxValue) || 0;
    return total + taxValue;
  }, 0);

  console.log('Active taxes:', activeTaxes.map(tax => `${tax.name}: ${tax.taxValue}%`));
  console.log('Total tax calculation:', activeTaxes.map(tax => tax.taxValue).join(' + ') + ' = ' + totalTaxPercentage + '%');

  return totalTaxPercentage;
};

/**
 * Calculates tax amount for a given price
 * @param price - The base price
 * @param taxPercentage - The tax percentage to apply
 * @returns number - Tax amount
 */
export const calculateTaxAmount = (price: number, taxPercentage: number): number => {
  return (price * taxPercentage) / 100;
};

/**
 * Calculates total price including tax
 * @param basePrice - The base price before tax
 * @param taxPercentage - The tax percentage to apply
 * @returns object - Contains basePrice, taxAmount, and totalPrice
 */
export const calculatePriceWithTax = (basePrice: number, taxPercentage: number) => {
  const taxAmount = calculateTaxAmount(basePrice, taxPercentage);
  const totalPrice = basePrice + taxAmount;

  return {
    basePrice: parseFloat(basePrice.toFixed(2)),
    taxAmount: parseFloat(taxAmount.toFixed(2)),
    totalPrice: parseFloat(totalPrice.toFixed(2)),
    taxPercentage: taxPercentage
  };
};

/**
 * Gets the stored tax data from localStorage
 * @returns Tax[] | null - The stored tax data or null if not found
 */
export const getStoredTaxData = (): Tax[] | null => {
  const taxData = localStorage.getItem('restaurantTaxData');
  return taxData ? JSON.parse(taxData) : null;
};

/**
 * Gets the stored total tax percentage from localStorage
 * @returns number - The stored tax percentage or 0 if not found
 */
export const getStoredTaxPercentage = (): number => {
  const taxPercentage = localStorage.getItem('totalTaxPercentage');
  return taxPercentage ? parseFloat(taxPercentage) : 0;
};

/**
 * Gets the stored userId from localStorage
 * @returns string | null - The stored userId or null if not found
 */
export const getStoredUserId = (): string | null => {
  return localStorage.getItem('userid');
};

/**
 * Clears the stored userId from localStorage
 */
export const clearStoredUserId = (): void => {
  localStorage.removeItem('userid');
  localStorage.removeItem('restaurantTaxData');
  localStorage.removeItem('totalTaxPercentage');
  console.log('Cleared stored userId and tax data from localStorage');
};
