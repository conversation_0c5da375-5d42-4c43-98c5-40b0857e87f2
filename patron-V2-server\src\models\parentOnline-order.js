import mongoose from "mongoose";

const parentOnlineOrder = new mongoose.Schema({
    OrderNo:{
        type:String
    },
    subOnlineOrderId:{
        type:Array
    },
    customerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'PatronPalCustomer',
    },
    totalAmount: {
        type: Number
    }
})
const parentModel=mongoose.model('parentOnlineOrder',parentOnlineOrder);

export default parentModel;