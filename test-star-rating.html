<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Star Rating</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background-color: #f8f9fa;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .star-rating {
            text-align: center;
            margin-bottom: 20px;
            display: inline-block;
        }
        .star-rating input[type="radio"] {
            display: none;
        }
        .star-rating label {
            font-size: 35px;
            color: #ddd;
            cursor: pointer;
            margin: 0 2px;
            transition: color 0.2s;
            display: inline-block;
        }
        .star-rating label:hover,
        .star-rating label.active {
            color: #ffc107;
        }
        .star-rating input[type="radio"]:checked ~ label {
            color: #ffc107;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }
        .form-textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            resize: vertical;
            min-height: 80px;
        }
        .submit-button {
            background-color: #068af5;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .submit-button:hover {
            background-color: #0056b3;
        }
        .rating-label {
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="color: #068af5; text-align: center;">Test Star Rating</h1>
        
        <form id="reviewForm" action="#" method="POST">
            <input type="hidden" name="deviceId" value="test-device-id">
            <input type="hidden" name="customerId" value="test-customer-id">
            <input type="hidden" name="orderId" value="test-order-id">

            <label class="rating-label">⭐ Rate Your Overall Experience</label>
            <div class="star-rating" id="starRating">
                <input type="radio" name="rating" value="1" id="star1" required>
                <label for="star1" data-rating="1">★</label>
                <input type="radio" name="rating" value="2" id="star2">
                <label for="star2" data-rating="2">★</label>
                <input type="radio" name="rating" value="3" id="star3">
                <label for="star3" data-rating="3">★</label>
                <input type="radio" name="rating" value="4" id="star4">
                <label for="star4" data-rating="4">★</label>
                <input type="radio" name="rating" value="5" id="star5">
                <label for="star5" data-rating="5">★</label>
            </div>

            <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience..."></textarea>
            </div>

            <input type="submit" class="submit-button" value="Submit Review" />
        </form>
        
        <div id="result" style="margin-top: 20px; padding: 10px; background: #f0f8ff; border-radius: 5px; display: none;">
            <strong>Selected Rating:</strong> <span id="selectedRating">None</span>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const starLabels = document.querySelectorAll('.star-rating label');
            const radioInputs = document.querySelectorAll('.star-rating input[type="radio"]');
            const resultDiv = document.getElementById('result');
            const selectedRatingSpan = document.getElementById('selectedRating');
            
            // Add click event listeners to star labels
            starLabels.forEach(function(label) {
                label.addEventListener('click', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    
                    // Check the corresponding radio button
                    const radioInput = document.getElementById('star' + rating);
                    if (radioInput) {
                        radioInput.checked = true;
                    }
                    
                    // Update visual feedback
                    updateStarDisplay(rating);
                    
                    // Show result
                    selectedRatingSpan.textContent = rating + ' star' + (rating > 1 ? 's' : '');
                    resultDiv.style.display = 'block';
                });
            });
            
            // Add change event listeners to radio inputs
            radioInputs.forEach(function(input) {
                input.addEventListener('change', function() {
                    if (this.checked) {
                        const rating = parseInt(this.value);
                        updateStarDisplay(rating);
                        selectedRatingSpan.textContent = rating + ' star' + (rating > 1 ? 's' : '');
                        resultDiv.style.display = 'block';
                    }
                });
            });
            
            function updateStarDisplay(rating) {
                starLabels.forEach(function(label, index) {
                    const starRating = index + 1;
                    if (starRating <= rating) {
                        label.classList.add('active');
                        label.style.color = '#ffc107';
                    } else {
                        label.classList.remove('active');
                        label.style.color = '#ddd';
                    }
                });
            }
            
            // Form validation before submit
            document.getElementById('reviewForm').addEventListener('submit', function(e) {
                e.preventDefault(); // Prevent actual submission for testing
                
                const selectedRating = document.querySelector('input[name="rating"]:checked');
                if (!selectedRating) {
                    alert('Please select a star rating before submitting your review.');
                    return false;
                }
                
                alert('Form would be submitted with rating: ' + selectedRating.value + ' stars');
            });
        });
    </script>
</body>
</html>
