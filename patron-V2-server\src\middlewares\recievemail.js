import nodemailer from 'nodemailer'
import dotenv from 'dotenv'
import {google} from 'googleapis'
dotenv.config()
const REFRESH_TOKEN='1//04B1qksl9eh7TCgYIARAAGAQSNwF-L9IrgjVLU1fZjwc5vI2NutRU7iYQdZWvKjVfhYcb0ep16ZK5Cf5LVoO86PIcDR0tH9zfHjc'
const CLIENT_ID='81639033188-h5jgoamh0m9jqht34ugeat8drjsdai4j.apps.googleusercontent.com'
const CLIENT_SECRET='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'
const REDIRECT_URI='https://developers.google.com/oauthplayground'
const OAuthClient= new google.auth.OAuth2(
    CLIENT_ID,
    CLIENT_SECRET,
    REFRESH_TOKEN
)
OAuthClient.setCredentials({refresh_token:REFRESH_TOKEN})
const recieveMail = async (email, html, toEmail) => {
    const token=await OAuthClient.getAccessToken();
    console.log('token: ', token);
 
    console.log('email 1: ', email);
    let domain;
    if (process.env.NODE_ENV === 'production') {
        domain = true
    } else if (process.env.NODE_ENV === 'development') {
        domain = false
    }
    try {

        const transporter = nodemailer.createTransport({
            service: 'gmail',
            secure:domain,
            auth: {
                type: 'OAuth2',
                user: "<EMAIL>",
                clientId:CLIENT_ID,
                clientSecret:CLIENT_SECRET,
                refreshToken:REFRESH_TOKEN,
                accessToken:token
            },
            tls:{
                rejectUnauthorized:true
            }
        })
        await transporter.sendMail({
            from: email,
            to:toEmail,
            // to: ["<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"],
            subject:email,
            html: html
        })
        console.log("Send Eamil Success");
    } catch (error) {
        console.log(error, "email not sent");
    }
}
export default recieveMail;



