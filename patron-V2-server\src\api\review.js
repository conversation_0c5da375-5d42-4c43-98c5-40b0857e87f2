import product from '../models/product.js';
import reviewModel from '../models/Review.js';

export const postReview = async (req, res) => {
  try {
    const { customerId, rating, review, productId , Profession ,likes} = req.body;

    const ReviewData = new reviewModel({ customerId, rating, review, productId , Profession , likes});
    const savedReview = await ReviewData.save();
    res.status(201).json({
      _id: savedReview._id,
      customerId: savedReview.customerId,
      rating: savedReview.rating,
      review: savedReview.review,
      productId: savedReview.productId,
      Profession:savedReview.Profession,
      likes:savedReview.likes


    });
    // Update the Product document with the review _id
    const updatedProduct = await product.findOneAndUpdate(
      { _id: productId },  // Make sure this matches the structure of your request body 
      {
        $push: { reviewId: savedReview._id },
      },
      { new: true }
    );

    if (!updatedProduct) {
      return res.status(404).send('Product not found');
    }

   
  } catch (error) {
    console.error('Error:', error);
    res.status(400).send('Unable to save rating and review.');
  }
};


// Like review API
export const updateReview = async (req, res) => {
  try {
    const { reviewId } = req.params;

    // Find the review by ID
    const review = await reviewModel.findById(reviewId);

    if (!review) {
      return res.status(404).send('Review not found');
    }
 
    // Increment the like count (assuming 'likes' is a field in your Review model)
    review.likes = (review.likes || 0) + 1;

    // Save the updated review
    await review.save();

    // Optionally, update the product or any other related data

    res.json({ message: 'Review liked successfully', updatedReview: review });

  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Internal Server Error');
  }
};


export const deleteReview = async (req, res) => {
  try {
    const { reviewId } = req.params;
    console.log('Received params:', req.params);

    // Attempt to delete the review
    const data = await reviewModel.deleteOne({ _id: reviewId });

    // Check if a document was deleted
    if (data.deletedCount > 0) {
      res.send({ message: 'Review deleted successfully' });
    } else {
      res.status(404).send({ message: 'Review data not found or cannot be deleted' });
    }
  } catch (error) {
    console.error('Error:', error);
    res.status(500).send('Internal Server Error');
  }
};