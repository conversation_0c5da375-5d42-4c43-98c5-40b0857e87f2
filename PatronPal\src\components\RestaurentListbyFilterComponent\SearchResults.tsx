import React from 'react';
import RestaurantCard from './RestaurantCard';
import type { Restaurant } from '../../pages/Restaurant/types/types';
// interface Restaurant {
//     userId: any;
//     id: string;
//     name: string;
//     cuisine: string;
//     rating: number;
//     image: string;
//     deal?: string;
//     pizzaOffer?: string;
//     priceOff?: string;
//     price: number;
//     discountPrice?: number;
//     active: boolean;
// }

interface SearchResultsProps {
    restaurants: Restaurant[];
    searchQuery: string;
    visibleCount: number;
    onShowMore: () => void;
    hasMore: boolean;
    isMobile: boolean;
    userId?: string;
    onFavoriteToggle?: (restaurantId: string) => void;
    favoriteLoading?: boolean;
    error?: string | null;
    onRetry?: () => void;
}

const SearchResults: React.FC<SearchResultsProps> = ({
    restaurants,
    searchQuery,
    visibleCount,
    onShowMore,
    hasMore,
    isMobile,
    userId = '',
    onFavoriteToggle,
    favoriteLoading = false,
    error = null,
    onRetry
}) => {
    if (error) {
        return (
            <div className="text-center py-12 bg-white rounded-lg shadow-sm">
                <div className="mb-4">
                    <svg className="w-16 h-16 mx-auto text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">No Results Available</h3>
                <p className="text-gray-500 mb-6">
                    We couldn't find any restaurants matching your search criteria.
                </p>
                {onRetry && (
                    <button
                        onClick={onRetry}
                        className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 cursor-pointer font-medium inline-flex items-center"
                    >
                        <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                        </svg>
                        Try Again
                    </button>
                )}
            </div>
        );
    }

    if (restaurants.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-500 text-lg mb-2">
                    No results found for "{searchQuery}"
                </div>
                <div className="text-gray-400">
                    Try searching for different restaurants, cuisines, or dishes
                </div>
            </div>
        );
    }

    return (
        <div className="mb-8">
            <div className="text-xl font-semibold text-[#19191C] mb-4">
                Search Results for "{searchQuery}" ({restaurants.length} found)
            </div>
            <div className={`grid ${isMobile ? 'grid-cols-1 sm:grid-cols-2' : 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'} gap-4`}>
                {restaurants.slice(0, visibleCount).map(restaurant => (
                    <div key={restaurant.id} className="w-full">
                        <RestaurantCard
                            restaurant={restaurant as any}
                            userId={userId}
                            onFavoriteToggle={onFavoriteToggle}
                            favoriteLoading={favoriteLoading}
                        />
                    </div>
                ))}
            </div>

            {/* Show More Button */}
            {hasMore && (
                <div className="flex justify-center mt-6">
                    <button
                        onClick={onShowMore}
                        className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors duration-200 cursor-pointer font-medium"
                    >
                        Show More
                    </button>
                </div>
            )}
        </div>
    );
};

export default SearchResults;
