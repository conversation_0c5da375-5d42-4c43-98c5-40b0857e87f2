/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useEffect } from 'react';
import { Heart, ShoppingCart } from 'lucide-react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { selectCurrentCustomer } from '../redux-store/slices/customerSlice';
import { selectUserFavoriteDevices } from '../redux-store/slices/deviceSlice';
import { useAppSelector, useAppDispatch } from "../redux-store/hooks";
import {
  selectFormattedAddress,
  selectDeliveryOption,
  type AddressData,
  setFullAddressData,
  setFormattedAddress
} from "../redux-store/slices/addressSlice";
import { selectCartItemCount } from "../redux-store/slices/cartSlice";
import AddressDropdown from "./AddressDropdown";

const Navbar = () => {
  // Add navigate
  const navigate = useNavigate();
  const location = useLocation();

  // Redux state type
  interface RootState {
    customer: any;
    auth?: {
      user?: {
        _id: string;
      };
    };
  }

  // Get user ID first
  const userId = useSelector((state: RootState) => state.customer?.currentCustomer?._id);

  // Get favorites data - use the new selector that calculates from devices array
  const favoriteDevices = useSelector(selectUserFavoriteDevices(userId || ''));
  const hasFavorites = favoriteDevices && favoriteDevices.length > 0;

  // Debug logging
  useEffect(() => {
    console.log('[Navbar] Favorites data:', {
      favoriteDevices,
      favoritesCount: favoriteDevices?.length || 0,
      hasFavorites,
      userId
    });
  }, [favoriteDevices, hasFavorites, userId]);

  const goToFavorites = () => {
    navigate('/favorites'); // Change '/favorites' to your actual route
  };

  const currentCustomer = useSelector(selectCurrentCustomer);

  // Address state from Redux
  const address = useAppSelector(selectFormattedAddress);
  // const fullAddressData = useAppSelector(selectFullAddressData);
  const selectedOption = useAppSelector(selectDeliveryOption);
  const dispatch = useAppDispatch();

  // Get cart item count from Redux
  const cartItemCount = useAppSelector(selectCartItemCount);

  // Dropdown state
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Note: No need to load favorites separately since we're calculating from devices array

  // Read URL parameters and update address state on component mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const stateParam = searchParams.get('state');
    const addressParam = searchParams.get('address');

    if (stateParam) {
      // If state parameter exists, create address data for the state
      const addressData: AddressData = {
        Line1: '',
        Line2: '',
        City: '',
        State: decodeURIComponent(stateParam),
        PostalCode: '',
        Country: 'United States'
      };

      // Force update the Redux state with URL parameter data
      dispatch(setFullAddressData(addressData));
      dispatch(setFormattedAddress(decodeURIComponent(stateParam)));

      // Also update localStorage to match URL parameter
      localStorage.setItem('lastSelectedAddress', JSON.stringify(addressData));
    } else if (addressParam) {
      // If address parameter exists, use it and clear any conflicting state data
      dispatch(setFormattedAddress(decodeURIComponent(addressParam)));

      // Clear localStorage if it contains state-only data that conflicts with address param
      const savedAddress = localStorage.getItem('lastSelectedAddress');
      if (savedAddress) {
        try {
          const parsedAddress = JSON.parse(savedAddress);
          if (parsedAddress.State && parsedAddress.Country === 'United States' && !parsedAddress.City) {
            // Remove state-only data when address param is present
            localStorage.removeItem('lastSelectedAddress');
          }
        } catch (error) {
          console.error('Error parsing saved address:', error);
        }
      }
    }
  }, [location.search, dispatch]);

  // Close dropdown when clicking outside
  useEffect(() => {
    console.log(isOpen)
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  },);

  // Handle address selection from dropdown
  const handleSelectAddress = (addressData: AddressData) => {
    dispatch(setFullAddressData(addressData));

    // Format the address for display in the input field and for filtering
    // For US states, show just the state name, for other cases show city, state, country
    let formattedAddress = '';
    if (addressData.State && addressData.Country === 'United States' && !addressData.City) {
      formattedAddress = addressData.State;
    } else {
      formattedAddress = [
        addressData.City,
        addressData.State,
        addressData.Country
      ].filter(Boolean).join(', ');
    }

    dispatch(setFormattedAddress(formattedAddress));

    // Save to localStorage for persistence
    localStorage.setItem('lastSelectedAddress', JSON.stringify(addressData));

    // First close the dropdown
    setIsOpen(false);

    // Update URL parameters if on products or restaurant-list page
    if (location.pathname.includes('/all-restaurants') || location.pathname.includes('/restaurant-list')) {
      const searchParams = new URLSearchParams(location.search);

     
      if (addressData.State && addressData.Country === 'United States' && !addressData.City) {
        searchParams.set('state', addressData.State);
        searchParams.delete('address'); 
      } else {
        searchParams.set('address', formattedAddress);
        searchParams.delete('state'); 
      }

      // Keep the existing type parameter or default to delivery
      const typeParam = searchParams.get('type') || selectedOption.toLowerCase();
      searchParams.set('type', typeParam);

      // Navigate to the same page with updated parameters
      navigate(`${location.pathname}?${searchParams.toString()}`, { replace: true });
    } else {
      // If not on products or restaurant-list page, navigate to products page
      if (addressData.State && addressData.Country === 'United States' && !addressData.City) {
        navigate(`/all-restaurants?type=${selectedOption.toLowerCase()}&state=${encodeURIComponent(addressData.State)}`);
      } else {
        const typeParam = selectedOption.toLowerCase();
        navigate(`/all-restaurants?type=${typeParam}&address=${formattedAddress}`);
      }
    }
  };


  return (
    <header className="sticky top-0 z-50 w-full bg-white shadow-md md:rounded-none rounded-b-xl">
      <div className=" w-full p-3 md:px-20  flex items-center justify-between">
        {/* User Avatar */}
        {userId && <Link className='block md:hidden' to="/profile-settings">

        
           <img
                  src={
                    currentCustomer?.profile_pic ||
                    'https://www.gstatic.com/images/branding/product/1x/avatar_circle_blue_512dp.png'
                  }
                  alt="User"
                  className="w-9 h-9 rounded-full object-cover border-2 border-white"
                />
        </Link>}
        {/* Logo */}
        <a href="/" className="md:text-xl text-base font-semibold text-black">
          Patron<span className="text-primary">Pal</span>
        </a>

        
        <div className="hidden md:flex items-center justify-end space-x-4 lg:w-[40%] md:w-[70%]">
          {/* Location Selector - Updated to match Home.tsx */}
          <div className="relative w-full border rounded-xl border-gray-200" ref={dropdownRef}>
            <AddressDropdown
              value={address}
              onSelectAddress={handleSelectAddress}
              autoFocus={false}
            />
          </div>

          {/* User Avatar */}
          {userId && <Link to="/profile-settings">
            <img
                  src={
                    currentCustomer?.profile_pic ||
                    'https://www.gstatic.com/images/branding/product/1x/avatar_circle_blue_512dp.png'
                  }
                  alt="User"
                  className="w-16 h-10 rounded-full object-cover border-2 border-white"
                />
          </Link>}

          {/* Favorites */}
          <div
            className="flex items-center text-sm text-gray-700 cursor-pointer"
            onClick={goToFavorites}
          >
            <Heart className={`w-5 h-5 mr-1 transition-all duration-200 ${
              hasFavorites
                ? 'fill-current text-orange-500'
                : 'text-gray-400 hover:text-orange-500'
            }`} />
            Favorites
          </div>

          {/* Cart */}
          <Link to='/cart' className="flex items-center text-sm text-gray-700 cursor-pointer relative">
            <ShoppingCart className="w-5 h-5 mr-1" />
            Cart
            {cartItemCount > 0 && (
              <span className="text-gray-500 text-base flex items-center justify-center mb-3 ml-1 border boder-orange-500 rounded-full h-5 w-5 bg-orange-500 text-white text-md text-bold text-center">
                {cartItemCount > 99 ? '99+' : cartItemCount}
              </span>
            )}
          </Link>
        </div>

        {/* Mobile Icons */}
        <div className="flex md:hidden items-center space-x-4">
          {/* Heart Icon */}
          <Heart
            className={`w-6 h-6 cursor-pointer transition-all duration-200 ${
              hasFavorites
                ? 'fill-current text-orange-500'
                : 'text-gray-400 hover:text-orange-500'
            }`}
            onClick={goToFavorites}
          />

          {/* Cart Icon with Badge */}
          <Link to='/cart' className="flex justify-end items-center relative cursor-pointer">
            <ShoppingCart className="w-6 h-6 text-gray-700" />
            {cartItemCount > 0 && (
              <span className="text-gray-500 text-base flex items-center justify-center mb-3 border boder-orange-500 rounded-full h-5 w-5 bg-orange-500 text-white text-bold">
                {cartItemCount > 99 ? '99+' : cartItemCount}
              </span>
            )}
          </Link>
        </div>
      </div>

      {/* Mobile Location Bar - Updated to match Home.tsx */}
      <div className="block md:hidden px-3 py-2 pb-3">
        <div className="relative border rounded-xl border-gray-200" ref={dropdownRef}>
          <AddressDropdown
            value={address}
            onSelectAddress={handleSelectAddress}
            autoFocus={false}
          />
        </div>
      </div>

    </header>
  );
};

export default Navbar;
