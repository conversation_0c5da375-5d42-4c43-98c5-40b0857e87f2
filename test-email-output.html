
  <html>
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <style>
        body {
          font-family: 'Poppins', sans-serif;
          background-color: #f8f8f8;
          margin: 0;
          padding: 0;
        }
        .container {
          width: 90%;
          max-width: 600px;
          margin: 0 auto;
          background: #ffffff;
          padding: 30px;
          border-radius: 10px;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header-bar {
          height: 4rem;
          background-color: #068af5;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        .logo {
          display: block;
          margin: 30px auto;
          max-width: 180px;
        }
        .order-details {
          background-color: #e8f4fd;
          padding: 15px;
          border-left: 4px solid #068af5;
          margin: 20px 0;
          border-radius: 5px;
        }
        .order-details h3 {
          margin: 0 0 10px;
          color: #068af5;
        }
        .review-section {
          background-color: #f0f8ff;
          padding: 25px;
          border-radius: 8px;
          border: 2px solid #068af5;
          margin: 30px 0;
        }
        .review-form {
          background-color: #fff;
          padding: 20px;
          border-radius: 8px;
          border: 1px solid #ddd;
        }
        .rating-label {
          font-weight: bold;
          margin-bottom: 10px;
          display: block;
          color: #333;
        }
        .star-rating {
          text-align: center;
          margin-bottom: 20px;
          display: inline-block;
        }
        .star-rating input[type="radio"] {
          display: none;
        }
        .star-rating label {
          font-size: 35px;
          color: #ddd;
          cursor: pointer;
          margin: 0 2px;
          transition: color 0.2s;
          display: inline-block;
        }
        .star-rating label:hover,
        .star-rating label.active {
          color: #ffc107;
        }
        .star-rating input[type="radio"]:checked ~ label {
          color: #ffc107;
        }

        #selectedRating {
          display: none;
        }
        .form-group {
          margin-bottom: 15px;
        }
        .form-label {
          display: block;
          font-weight: bold;
          margin-bottom: 5px;
          color: #333;
        }
        .form-textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;
          min-height: 80px;
          resize: vertical;
        }
        .submit-button {
          background-color: #068af5;
          color: white;
          padding: 12px 30px;
          border: none;
          border-radius: 5px;
          font-weight: bold;
          cursor: pointer;
          font-size: 16px;
          transition: background-color 0.3s;
        }
        .submit-button:hover {
          background-color: #0056b3;
        }
        .footer {
          text-align: center;
          font-size: 13px;
          color: #888;
          margin-top: 30px;
        }
        .social-icons img {
          width: 20px;
          margin: 0 5px;
        }
        @media screen and (max-width: 600px) {
          .container {
            padding: 20px;
          }
          .logo {
            max-width: 140px;
          }
        }
      </style>
      <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap" rel="stylesheet">
    </head>
    <body>
      <div class="header-bar"></div>
      <div class="container">
        <img class="logo" src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690281194/WhatsApp_Image_2023-07-25_at_3.32.42_PM_gswsmd.jpg" alt="Logo" />

        <p>Dear <strong>John Doe</strong>,</p>

        <div class="order-details">
          <h3>🎉 Great News! Your Order is Ready</h3>
          <p><strong>Order Number:</strong> ORD-12345</p>
          <p><strong>Status:</strong> Your order is <strong>ready for pickup</strong>.</p>
          <p>Please come to pick up your order.</p>
        </div>

        <p>Thank you for choosing <strong>Test Restaurant</strong>.</p>
        <p>Please visit our location to collect your order at your convenience.</p>

        <div class="review-section">
          <h3>📝 We Value Your Feedback!</h3>
          <p>Your thoughts help us serve you better. Please take a moment to share your experience.</p>

          <div class="review-form">
            <form id="reviewForm" action="http://localhost:4444/api/v1/device/email-review" method="POST">
              <input type="hidden" name="deviceId" value="507f1f77bcf86cd799439011">
              <input type="hidden" name="customerId" value="507f1f77bcf86cd799439013">
              <input type="hidden" name="orderId" value="507f1f77bcf86cd799439012">

              <label class="rating-label">⭐ Rate Your Overall Experience</label>
              <div class="star-rating" id="starRating">
                <input type="radio" name="rating" value="1" id="star1" required>
                <label for="star1" data-rating="1">★</label>
                <input type="radio" name="rating" value="2" id="star2">
                <label for="star2" data-rating="2">★</label>
                <input type="radio" name="rating" value="3" id="star3">
                <label for="star3" data-rating="3">★</label>
                <input type="radio" name="rating" value="4" id="star4">
                <label for="star4" data-rating="4">★</label>
                <input type="radio" name="rating" value="5" id="star5">
                <label for="star5" data-rating="5">★</label>
              </div>

              <div class="form-group">
                <label class="form-label" for="testimonial">Tell us more (optional)</label>
                <textarea class="form-textarea" name="testimonial" id="testimonial" placeholder="Share your experience at Test Restaurant..."></textarea>
              </div>

              <input type="submit" class="submit-button" value="Submit Review" />
            </form>
            <p style="font-size: 13px; color: #555;"><em>Thank you for your feedback!</em></p>
          </div>
        </div>

        <p>If you have any questions, feel free to reach out at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>

        <div class="footer">
          <p>Follow us:</p>
          <div class="social-icons">
            <a href="https://www.linkedin.com/company/patronworks/"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439086/WhatsApp_Image_2023-07-27_at_11.12.37_AM_1_whbn0t.jpg" alt="LinkedIn" /></a>
            <a href="https://www.facebook.com/patronworks"><img src="https://res.cloudinary.com/drfdk5lxw/image/upload/v1690439056/WhatsApp_Image_2023-07-27_at_11.12.37_AM_yedkyi.jpg" alt="Facebook" /></a>
          </div>
          <p>&copy; 2025 Test Restaurant. All rights reserved.</p>
        </div>
      </div>

      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const starLabels = document.querySelectorAll('.star-rating label');
          const radioInputs = document.querySelectorAll('.star-rating input[type="radio"]');

          // Add click event listeners to star labels
          starLabels.forEach(function(label) {
            label.addEventListener('click', function() {
              const rating = parseInt(this.getAttribute('data-rating'));

              // Check the corresponding radio button
              const radioInput = document.getElementById('star' + rating);
              if (radioInput) {
                radioInput.checked = true;
              }

              // Update visual feedback
              updateStarDisplay(rating);
            });
          });

          // Add change event listeners to radio inputs
          radioInputs.forEach(function(input) {
            input.addEventListener('change', function() {
              if (this.checked) {
                const rating = parseInt(this.value);
                updateStarDisplay(rating);
              }
            });
          });

          function updateStarDisplay(rating) {
            starLabels.forEach(function(label, index) {
              const starRating = index + 1;
              if (starRating <= rating) {
                label.classList.add('active');
                label.style.color = '#ffc107';
              } else {
                label.classList.remove('active');
                label.style.color = '#ddd';
              }
            });
          }

          // Form validation before submit
          document.getElementById('reviewForm').addEventListener('submit', function(e) {
            const selectedRating = document.querySelector('input[name="rating"]:checked');
            if (!selectedRating) {
              e.preventDefault();
              alert('Please select a star rating before submitting your review.');
              return false;
            }
          });
        });
      </script>

    </body>
  </html>
  