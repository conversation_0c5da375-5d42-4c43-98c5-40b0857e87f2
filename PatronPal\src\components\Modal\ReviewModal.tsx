/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, {useEffect, useState } from "react";
import { X } from "lucide-react";
import apiClient from "../../redux-store/config";
import StarRating from "../StarRating/StarRating";

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  restaurantName: string;
  restaurantImage: string;
  rating: number;
  reviewCount: number;
  restaurantId: string;
  // Product data passed directly through props
  productData?: any | null; // Changed to any to handle both product and device data
}

interface Review {
  _id: string;
  rating?: number;
  review?: string; // For product reviews
  testimonial?: string; // For device reviews
  customerId?: {
    name?: string;
    email?: string;
    FirstName?: string;
    LastName?: string;
    Email?: string;
  };
  creatdateFormat?: string; // For product reviews
  createdDate?: string; // For device reviews
  Profession?: string;
  likes?: number;
  // Device review fields
  food?: number;
  service?: number;
  ambiance?: number;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  isOpen,
  onClose,
  restaurantName,
  restaurantImage,
  rating,
  reviewCount,
  productData = null,
}) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [averageRating, setAverageRating] = useState<number>(0);
  const [totalReviews, setTotalReviews] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);

  // Fetch reviews when modal opens and productData is available
  useEffect(() => {
    if (isOpen && productData) {
      console.log('ProductData received:', productData);

      // Check if this is device data with reviews array (restaurant reviews)
      if (productData.reviews && Array.isArray(productData.reviews)) {
        console.log('Using device reviews:', productData.reviews);
        handleDeviceReviews(productData.reviews);
      }
      // Check if this is product data with reviewId array (product reviews)
      else if (productData.reviewId && Array.isArray(productData.reviewId)) {
        console.log('Using product reviews:', productData.reviewId);
        handleProductReviews(productData.reviewId);
      }
      // Try to fetch from API if we have an ID
      else if (productData._id) {
        console.log('Fetching reviews from API for ID:', productData._id);
        fetchReviews(productData._id);
      }
      else {
        console.log('No reviews found in productData');
        // Set empty state
        setReviews([]);
        setAverageRating(rating || 0);
        setTotalReviews(reviewCount || 0);
      }
    }
  }, [isOpen, productData, rating, reviewCount]);

  const fetchReviews = async (productId: string) => {
    setLoading(true);
    try {
      console.log('Fetching reviews for productId:', productId);
      const response = await apiClient.get(`/reviews/product/${productId}?limit=10`);
      const data = response.data;
      console.log('Reviews API response:', data);
      setReviews(data.reviews || []);
      setAverageRating(data.averageRating || 0);
      setTotalReviews(data.totalReviews || 0);
    } catch (error) {
      console.error('Error fetching reviews:', error);
      // Fallback to existing product data if API fails
      fallbackToProductData();
    } finally {
      setLoading(false);
    }
  };

  const handleDeviceReviews = (deviceReviews: any[]) => {
    // Sort reviews by creation date (most recent first) and take 10
    const sortedReviews = [...deviceReviews]
      .sort((a, b) => new Date(b.createdDate || 0).getTime() - new Date(a.createdDate || 0).getTime())
      .slice(0, 10);

    // Process device reviews (restaurant reviews) - only use reviews with rating field
    const processedReviews = sortedReviews
      .filter(review => review.rating && review.rating > 0) // Only include reviews with rating
      .map(review => ({
        _id: review._id || Math.random().toString(),
        rating: review.rating,
        testimonial: review.testimonial || '',
        customerId: review.customerId || { name: 'Customer' }, // Use actual customer data if available
        createdDate: review.createdDate
      }));

    setReviews(processedReviews);

    // Calculate average rating from ALL device reviews (not just the 10 displayed)
    const allValidRatings = deviceReviews.filter(r => r.rating && r.rating > 0);

    const avgRating = allValidRatings.length > 0
      ? allValidRatings.reduce((sum, r) => sum + r.rating!, 0) / allValidRatings.length
      : 0;

    setAverageRating(isNaN(avgRating) ? 0 : avgRating);
    // Only count reviews that have the rating field (email reviews)
    setTotalReviews(allValidRatings.length);
  };

  const handleProductReviews = (productReviews: any[]) => {
    // Process product reviews
    const processedReviews = productReviews.slice(0, 10);
    setReviews(processedReviews);
    setAverageRating(rating);
    setTotalReviews(reviewCount);
  };

  const fallbackToProductData = () => {
    if (productData?.reviewId && Array.isArray(productData.reviewId)) {
      handleProductReviews(productData.reviewId);
    } else if (productData?.reviews && Array.isArray(productData.reviews)) {
      handleDeviceReviews(productData.reviews);
    }
  };

  if (!isOpen) {
    return null;
  }

  // Format date helper
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Display values
  const displayName = productData?.name || restaurantName;
  const displayAverageRating = averageRating || rating || 0;
  const displayTotalReviews = totalReviews || reviewCount || 0;

  // Format review count (1,2,3,4,5 or 5+ for more than 5)
  const formatReviewCount = (count: number) => {
    if (count <= 5) return count.toString();
    return '5+';
  };




  return (
    <div
      className="fixed inset-0 backdrop-blur flex items-center justify-center z-50 px-2"
      style={{ backgroundColor: "#00000033" }}
    >
      <div className="relative w-full max-w-[700px] bg-white rounded-lg shadow-lg max-h-[90vh] overflow-hidden">
        {/* Header with close button */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-xl font-semibold text-[#19191C]">Reviews</h3>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Restaurant info with image */}
        <div className="px-4 py-3 border-b">
          <div className="flex items-center gap-3">
            {restaurantImage && (
              <img
                src={restaurantImage}
                alt={displayName}
                className="w-12 h-12 rounded-lg object-cover"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <h4 className="text-lg font-medium text-[#19191C]">{displayName}</h4>
          </div>
        </div>

        {/* Average Rating Section */}
        <div className="px-4 py-4 border-b bg-gray-50">
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-[#19191C] mb-1">
                {displayAverageRating > 0 ? displayAverageRating.toFixed(1) : 'New'}
              </div>
              <div className="flex items-center justify-center mb-1">
                <StarRating rating={displayAverageRating} size={16} />
              </div>
              <div className="text-sm text-gray-600">
                {formatReviewCount(displayTotalReviews)} review{displayTotalReviews !== 1 ? 's' : ''}
              </div>
            </div>
          </div>
        </div>

        {/* Reviews List */}
        <div className="px-4 py-4 max-h-[450px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500">Loading reviews...</div>
            </div>
          ) : reviews.length > 0 ? (
            <div className="space-y-4">
              {reviews.map((review) => (
                <div key={review._id} className="border-b border-gray-200 pb-4 last:border-b-0">
                  <div className="flex items-start gap-3">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <span className="font-medium text-[#19191C]">
                          {review.customerId?.FirstName && review.customerId?.LastName
                            ? `${review.customerId.FirstName} ${review.customerId.LastName}`
                            : review.customerId?.name || review.customerId?.FirstName || review.customerId?.LastName || 'Anonymous'}
                        </span>
                        <div className="flex items-center">
                          <StarRating rating={review.rating || 0} size={14} />
                        </div>
                        <span className="text-sm text-gray-500">
                          {formatDate(review.creatdateFormat || review.createdDate || '')}
                        </span>
                      </div>
                      {(review.review || review.testimonial) && (
                        <p className="text-gray-700 text-sm leading-relaxed">
                          {review.review || review.testimonial}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-500 text-center">
                <div className="text-lg font-medium mb-2">No Reviews Yet</div>
                <div className="text-sm">Be the first to review this product!</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReviewModal;