/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";

// Category interface
interface Category {
  _id: string;
  name: string;
  extraData?: any;
  categoryType?: string;
  category_pic?: string;
  order?: number;
  hasPicture: boolean;
  active: boolean;
  parentId?: any;
  lampixIcon?: string;
  translation?: any;
  product?: any;
  showPictures?: boolean;
  userId: any;
  __v?: number;
}

// Request interfaces
interface CategoryCreateRequest {
  name: string;
  extraData?: any;
  categoryType?: string;
  category_pic?: string;
  order?: number;
  hasPicture: boolean;
  active: boolean;
  parentId?: string;
  lampixIcon?: string;
  translation?: any;
  product?: any;
  showPictures?: boolean;
  userId: string;
}

interface CategoryUpdateRequest extends CategoryCreateRequest {
  _id: string;
  __v?: number;
}

interface CategoryFilters {
  parentId?: string;
  userId?: string;
}

// State interface
interface CategoryState {
  categories: Category[];
  currentCategory: Category | null;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: CategoryState = {
  categories: [],
  currentCategory: null,
  loading: false,
  error: null,
};

// Async thunks
export const getCategories = createAsyncThunk<
  Category[],
  CategoryFilters,
  { rejectValue: string }
>("category/getCategories", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.parentId) params.append("parentId", filters.parentId);
    if (filters.userId) params.append("userId", filters.userId);

    const response = await apiClient.get<Category[]>(
      `/category?${params.toString()}`
    );

    console.log("[getCategories] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to fetch categories";
    return rejectWithValue(message);
  }
});

export const getCategoryById = createAsyncThunk<
  Category,
  string,
  { rejectValue: string }
>("category/getCategoryById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Category>(`/category/${id}`);
    console.log("[getCategoryById] response:", response.data);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch category";
    return rejectWithValue(message);
  }
});

export const createCategory = createAsyncThunk<
  Category,
  { categoryData: CategoryCreateRequest; file?: File },
  { rejectValue: string }
>(
  "category/createCategory",
  async ({ categoryData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all category data to formData
      Object.entries(categoryData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Handle complex objects by stringifying them
          if (typeof value === "object") {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value.toString());
          }
        }
      });

      // Append file if provided
      if (file) {
        formData.append("category_pic", file);
      }

      const response = await apiClient.post<Category>("/category", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to create category";
      return rejectWithValue(message);
    }
  }
);

export const updateCategory = createAsyncThunk<
  Category,
  { id: string; categoryData: CategoryUpdateRequest; file?: File },
  { rejectValue: string }
>(
  "category/updateCategory",
  async ({ id, categoryData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all category data to formData
      Object.entries(categoryData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          // Handle complex objects by stringifying them
          if (typeof value === "object") {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value.toString());
          }
        }
      });

      // Append file if provided
      if (file) {
        formData.append("category_pic", file);
      }

      const response = await apiClient.put<{
        data: Category;
        message: string;
      }>(`/category/${id}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to update category";
      return rejectWithValue(message);
    }
  }
);

export const deleteCategory = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("category/deleteCategory", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete<{ message: string }>(`/category/${id}`);
    return id; // Return the deleted category ID
  } catch (error: any) {
    const message =
      error.response?.data?.message || "Failed to delete category";
    return rejectWithValue(message);
  }
});

// Category slice
const categorySlice = createSlice({
  name: "category",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentCategory: (state, action: PayloadAction<Category>) => {
      state.currentCategory = action.payload;
    },
    clearCurrentCategory: (state) => {
      state.currentCategory = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Get categories
      .addCase(getCategories.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategories.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = action.payload;
      })
      .addCase(getCategories.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch categories";
      })

      // Get category by ID
      .addCase(getCategoryById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCategoryById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentCategory = action.payload;
      })
      .addCase(getCategoryById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch category";
      })

      // Create category
      .addCase(createCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.categories.push(action.payload);
      })
      .addCase(createCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create category";
      })

      // Update category
      .addCase(updateCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCategory.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.categories.findIndex(
          (c) => c._id === action.payload._id
        );
        if (index !== -1) {
          state.categories[index] = action.payload;
        }
        if (
          state.currentCategory &&
          state.currentCategory._id === action.payload._id
        ) {
          state.currentCategory = action.payload;
        }
      })
      .addCase(updateCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update category";
      })

      // Delete category
      .addCase(deleteCategory.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteCategory.fulfilled, (state, action) => {
        state.loading = false;
        state.categories = state.categories.filter(
          (c) => c._id !== action.payload
        );
        if (
          state.currentCategory &&
          state.currentCategory._id === action.payload
        ) {
          state.currentCategory = null;
        }
      })
      .addCase(deleteCategory.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete category";
      });
  },
});

// Export actions
export const { clearError, setCurrentCategory, clearCurrentCategory } =
  categorySlice.actions;

// Selectors
export const selectCategories = (state: { category: CategoryState }) =>
  state.category.categories;
export const selectCurrentCategory = (state: { category: CategoryState }) =>
  state.category.currentCategory;
export const selectCategoryLoading = (state: { category: CategoryState }) =>
  state.category.loading;
export const selectCategoryError = (state: { category: CategoryState }) =>
  state.category.error;

// Export reducer
export default categorySlice.reducer;
