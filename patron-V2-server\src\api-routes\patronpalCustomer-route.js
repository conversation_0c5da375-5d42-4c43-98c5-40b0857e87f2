import express from 'express';
import {
    customerRegister,
    customerLogin,
    sendUserPasswordResetOTP,
    confirmUserPasswordResetOTP,
    confirmEmailVerification,
    updateUserPassword,
    addUserToCustomer,
    updateCustomer,
    signupByGoogle,
    loginByGoogle
} from '../api/patronpalCustomer.js';

const router = express.Router();

// POST /api/customers/register
router.post('/Pregister', customerRegister);

// POST /api/customers/login
router.post('/Plogin', customerLogin);

// PUT /api/customers/update-profile
router.put('/update-profile/:userId', updateCustomer);

// POST /api/customers/forgot-password
router.post('/Pforgot-password', sendUserPasswordResetOTP);

// POST /api/customers/forgot-password
router.post('/PGoogleRegister', signupByGoogle)


// POST /api/customers/forgot-password
router.post('/PGoogleLogin', loginByGoogle)

// POST /api/customers/confirm-otp
router.post('/Pconfirm-otp', confirmUserPasswordResetOTP);

// POST /api/customers/verify-email
router.post('/Pverify-email', confirmEmailVerification);

// POST /api/customers/update-password
router.post('/Pupdate-password', updateUserPassword);

// POST /api/customers/add-user
router.post('/Padd-user', addUserToCustomer);

export default router;
